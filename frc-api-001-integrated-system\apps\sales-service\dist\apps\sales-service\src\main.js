"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = require("dotenv");
const core_1 = require("@nestjs/core");
const sales_service_module_1 = require("./sales-service.module");
const path = require("path");
const envPath = path.join(process.cwd(), '.env');
console.log('Loading .env from:', envPath);
(0, dotenv_1.config)({ path: envPath });
console.log('Environment variables loaded:');
console.log('SALES_MICROSERVICE_HOST:', process.env.SALES_MICROSERVICE_HOST);
console.log('SALES_MICROSERVICE_PORT:', process.env.SALES_MICROSERVICE_PORT);
async function bootstrap() {
    const app = await core_1.NestFactory.create(sales_service_module_1.SalesServiceModule);
    app.enableCors();
    const port = process.env.SALES_SERVICE_PORT || process.env.PORT || 3003;
    const host = process.env.SALES_SERVICE_HOST || 'localhost';
    await app.listen(port);
    console.log(`🚀 Sales Service is running on: http://${host}:${port}`);
}
bootstrap();
//# sourceMappingURL=main.js.map