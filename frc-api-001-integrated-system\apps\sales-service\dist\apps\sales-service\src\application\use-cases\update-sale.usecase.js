"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSaleUseCase = exports.UPDATE_SALE_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const update_sale_transaction_1 = require("../../domain/ports/transactions/update-sale.transaction");
const sale_builder_1 = require("../../domain/builders/sale.builder");
const customer_builder_1 = require("../../domain/builders/customer.builder");
const location_builder_1 = require("../../domain/builders/location.builder");
const jwt_service_1 = require("../../domain/ports/services/jwt.service");
exports.UPDATE_SALE_USE_CASE = Symbol('UPDATE_SALE_USE_CASE');
let UpdateSaleUseCase = class UpdateSaleUseCase {
    _updateSaleTransaction;
    _jwtService;
    saleBuilder;
    customerBuilder;
    locationBuilder;
    constructor(_updateSaleTransaction, _jwtService) {
        this._updateSaleTransaction = _updateSaleTransaction;
        this._jwtService = _jwtService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
        this.customerBuilder = new customer_builder_1.CustomerBuilder();
        this.locationBuilder = new location_builder_1.LocationBuilder();
    }
    async execute(saleId, saleData, token) {
        const userInfo = await this._jwtService.verify(token);
        const sale = {
            sale_id: saleId,
            policy_number: saleData.policyNumber,
            start_sale_date: new Date(saleData.startSaleDate),
            end_sale_date: new Date(saleData.endSaleDate),
            net_amount: saleData.netAmount,
            total_amount: saleData.totalAmount,
            condition_sale_id: saleData.conditionSaleId,
            currency_id: saleData.currencyId,
            cia_company_id: saleData.ciaCompanyId,
            insurance_line_cia_sale_id: saleData.insuranceLineCiaSaleId,
            account_id: userInfo.accountId
        };
        let customer = undefined;
        if (saleData.customer) {
            customer = {
                customer_id: saleData.customer.customerId,
                full_name: saleData.customer.fullName,
                doi: saleData.customer.doi,
                phone_number: saleData.customer.phoneNumber,
                mobile_number: saleData.customer.mobileNumber,
                document_type_id: saleData.customer.documentTypeId
            };
        }
        let location = undefined;
        if (saleData.location) {
            location = {
                location_id: saleData.location.locationId,
                address: saleData.location.address,
                department: saleData.location.department,
                province: saleData.location.province,
                district: saleData.location.district
            };
        }
        const updateSaleDto = {
            saleId,
            sale,
            customer,
            location,
            accountId: userInfo.accountId
        };
        return await this._updateSaleTransaction.run(updateSaleDto);
    }
};
exports.UpdateSaleUseCase = UpdateSaleUseCase;
exports.UpdateSaleUseCase = UpdateSaleUseCase = __decorate([
    __param(0, (0, common_1.Inject)(update_sale_transaction_1.UPDATE_SALE_TRANSACTION)),
    __param(1, (0, common_1.Inject)(jwt_service_1.JWT_SERVICE)),
    __metadata("design:paramtypes", [Object, Object])
], UpdateSaleUseCase);
//# sourceMappingURL=update-sale.usecase.js.map