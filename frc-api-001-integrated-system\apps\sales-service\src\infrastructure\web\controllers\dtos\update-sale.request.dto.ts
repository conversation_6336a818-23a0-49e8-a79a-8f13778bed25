import { IsString, IsN<PERSON>ber, IsOptional, IsDate, IsObject, ValidateNested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateCustomerDto, CreateLocationDto, CreateSaleInfoDto } from './create-sale.request.dto';

/**
 * DTO para la solicitud de actualización de una venta completa
 * Extiende los DTOs de creación pero agrega el ID de la venta
 */
export class UpdateSaleRequestDto {
  @IsNumber()
  @IsNotEmpty()
  saleId: number;

  @IsObject()
  @ValidateNested()
  @Type(() => CreateSaleInfoDto)
  @IsNotEmpty()
  sale: CreateSaleInfoDto;

  @IsObject()
  @ValidateNested()
  @Type(() => CreateCustomerDto)
  @IsNotEmpty()
  customer: CreateCustomerDto;

  @IsObject()
  @ValidateNested()
  @Type(() => CreateLocationDto)
  @IsOptional()
  location?: CreateLocationDto;
}
