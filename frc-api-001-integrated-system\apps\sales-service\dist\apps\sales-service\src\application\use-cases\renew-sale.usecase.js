"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RenewSaleUseCase = exports.RENEW_SALE_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const renew_sale_transaction_1 = require("../../domain/ports/transactions/renew-sale.transaction");
const jwt_service_1 = require("../../domain/ports/services/jwt.service");
exports.RENEW_SALE_USE_CASE = Symbol('RENEW_SALE_USE_CASE');
let RenewSaleUseCase = class RenewSaleUseCase {
    _renewSaleTransaction;
    _jwtService;
    constructor(_renewSaleTransaction, _jwtService) {
        this._renewSaleTransaction = _renewSaleTransaction;
        this._jwtService = _jwtService;
    }
    async execute(originalSaleId, renewalData, token) {
        const userInfo = await this._jwtService.verify(token);
        const renewSaleDto = {
            originalSaleId,
            newSale: {
                net_amount: renewalData.netAmount,
                total_amount: renewalData.totalAmount
            },
            startDate: new Date(renewalData.startDate),
            endDate: new Date(renewalData.endDate),
            accountId: userInfo.accountId,
            keepCustomerInfo: renewalData.keepCustomerInfo !== undefined ? renewalData.keepCustomerInfo : true
        };
        return await this._renewSaleTransaction.run(renewSaleDto);
    }
};
exports.RenewSaleUseCase = RenewSaleUseCase;
exports.RenewSaleUseCase = RenewSaleUseCase = __decorate([
    __param(0, (0, common_1.Inject)(renew_sale_transaction_1.RENEW_SALE_TRANSACTION)),
    __param(1, (0, common_1.Inject)(jwt_service_1.JWT_SERVICE)),
    __metadata("design:paramtypes", [Object, Object])
], RenewSaleUseCase);
//# sourceMappingURL=renew-sale.usecase.js.map