import { config } from 'dotenv';
import { NestFactory } from '@nestjs/core';
import { SalesServiceModule } from './sales-service.module';
import * as path from 'path';

// Cargar variables de entorno desde .env en la raíz del módulo
// Cuando se ejecuta con npm start, el cwd es la raíz del sales-service
const envPath = path.join(process.cwd(), '.env');
console.log('Loading .env from:', envPath);
config({ path: envPath });

console.log('Environment variables loaded:');
console.log('SALES_MICROSERVICE_HOST:', process.env.SALES_MICROSERVICE_HOST);
console.log('SALES_MICROSERVICE_PORT:', process.env.SALES_MICROSERVICE_PORT);

async function bootstrap() {
  const app = await NestFactory.create(SalesServiceModule);
  
  // Configurar CORS para permitir peticiones desde el frontend
  app.enableCors();
  
  const port = process.env.SALES_SERVICE_PORT || process.env.PORT || 3003;
  const host = process.env.SALES_SERVICE_HOST || 'localhost';
  
  await app.listen(port);
  console.log(`🚀 Sales Service is running on: http://${host}:${port}`);
}
bootstrap();
