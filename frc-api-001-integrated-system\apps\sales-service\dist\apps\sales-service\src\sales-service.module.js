"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesServiceModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const web_module_1 = require("./infrastructure/web/web.module");
const http_exception_filter_1 = require("./infrastructure/web/filters/http-exception.filter");
const config_1 = require("./infrastructure/config");
const services_module_1 = require("./infrastructure/services/services.module");
const create_sale_usecase_1 = require("./application/use-cases/create-sale.usecase");
const update_sale_usecase_1 = require("./application/use-cases/update-sale.usecase");
const get_sale_by_id_usecase_1 = require("./application/use-cases/get-sale-by-id.usecase");
const get_sales_by_page_usecase_1 = require("./application/use-cases/get-sales-by-page.usecase");
const renew_sale_usecase_1 = require("./application/use-cases/renew-sale.usecase");
const delete_sale_usecase_1 = require("./application/use-cases/delete-sale.usecase");
const prisma_create_sale_transaction_1 = require("./infrastructure/persistence/transactions/prisma-create-sale.transaction");
const prisma_update_sale_transaction_1 = require("./infrastructure/persistence/transactions/prisma-update-sale.transaction");
const prisma_delete_sale_transaction_1 = require("./infrastructure/persistence/transactions/prisma-delete-sale.transaction");
const prisma_renew_sale_transaction_1 = require("./infrastructure/persistence/transactions/prisma-renew-sale.transaction");
const create_sale_transaction_1 = require("./domain/ports/transactions/create-sale.transaction");
const update_sale_transaction_1 = require("./domain/ports/transactions/update-sale.transaction");
const delete_sale_transaction_1 = require("./domain/ports/transactions/delete-sale.transaction");
const renew_sale_transaction_1 = require("./domain/ports/transactions/renew-sale.transaction");
const prisma_customer_repository_1 = require("./infrastructure/persistence/repositories/prisma-customer.repository");
const prisma_location_repository_1 = require("./infrastructure/persistence/repositories/prisma-location.repository");
const prisma_sale_repository_1 = require("./infrastructure/persistence/repositories/prisma-sale.repository");
const customer_repository_1 = require("./domain/ports/repositories/customer.repository");
const location_repository_1 = require("./domain/ports/repositories/location.repository");
const sale_repository_1 = require("./domain/ports/repositories/sale.repository");
const auth_service_1 = require("./infrastructure/services/auth/auth.service");
const jwt_service_1 = require("./domain/ports/services/jwt.service");
let SalesServiceModule = class SalesServiceModule {
};
exports.SalesServiceModule = SalesServiceModule;
exports.SalesServiceModule = SalesServiceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            web_module_1.WebModule,
            services_module_1.ServicesModule
        ],
        controllers: [],
        providers: [
            {
                provide: core_1.APP_FILTER,
                useClass: http_exception_filter_1.HttpExceptionFilter
            },
            {
                provide: create_sale_transaction_1.CREATE_SALE_TRANSACTION,
                useClass: prisma_create_sale_transaction_1.PrismaCreateSaleTransaction
            },
            {
                provide: update_sale_transaction_1.UPDATE_SALE_TRANSACTION,
                useClass: prisma_update_sale_transaction_1.PrismaUpdateSaleTransaction
            },
            {
                provide: delete_sale_transaction_1.DELETE_SALE_TRANSACTION,
                useClass: prisma_delete_sale_transaction_1.PrismaDeleteSaleTransaction
            },
            {
                provide: renew_sale_transaction_1.RENEW_SALE_TRANSACTION,
                useClass: prisma_renew_sale_transaction_1.PrismaRenewSaleTransaction
            },
            {
                provide: customer_repository_1.CUSTOMER_REPOSITORY,
                useClass: prisma_customer_repository_1.PrismaCustomerRepository
            },
            {
                provide: location_repository_1.LOCATION_REPOSITORY,
                useClass: prisma_location_repository_1.PrismaLocationRepository
            },
            {
                provide: sale_repository_1.SALE_REPOSITORY,
                useClass: prisma_sale_repository_1.PrismaSaleRepository
            },
            {
                provide: jwt_service_1.JWT_SERVICE,
                useFactory: (authService) => ({
                    verify: async (token) => {
                        const payload = authService.verifyToken(token);
                        return {
                            accountId: payload.accountId,
                            roleId: payload.roleId,
                            officeId: payload.officeId
                        };
                    }
                }),
                inject: [auth_service_1.AuthService]
            },
            {
                provide: create_sale_usecase_1.CREATE_SALE_USE_CASE,
                useClass: create_sale_usecase_1.CreateSaleUseCase
            },
            {
                provide: update_sale_usecase_1.UPDATE_SALE_USE_CASE,
                useClass: update_sale_usecase_1.UpdateSaleUseCase
            },
            {
                provide: get_sale_by_id_usecase_1.GET_SALE_BY_ID_USE_CASE,
                useClass: get_sale_by_id_usecase_1.GetSaleByIdUseCase
            },
            {
                provide: get_sales_by_page_usecase_1.GET_SALES_BY_PAGE_USE_CASE,
                useClass: get_sales_by_page_usecase_1.GetSalesByPageUseCase
            },
            {
                provide: renew_sale_usecase_1.RENEW_SALE_USE_CASE,
                useClass: renew_sale_usecase_1.RenewSaleUseCase
            },
            {
                provide: delete_sale_usecase_1.DELETE_SALE_USE_CASE,
                useClass: delete_sale_usecase_1.DeleteSaleUseCase
            }
        ],
    })
], SalesServiceModule);
//# sourceMappingURL=sales-service.module.js.map