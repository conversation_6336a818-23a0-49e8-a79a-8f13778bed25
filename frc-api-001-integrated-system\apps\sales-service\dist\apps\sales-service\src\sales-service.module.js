"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesServiceModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const web_module_1 = require("./infrastructure/web/web.module");
const http_exception_filter_1 = require("./infrastructure/web/filters/http-exception.filter");
const config_1 = require("./infrastructure/config");
const services_module_1 = require("./infrastructure/services/services.module");
let SalesServiceModule = class SalesServiceModule {
};
exports.SalesServiceModule = SalesServiceModule;
exports.SalesServiceModule = SalesServiceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            web_module_1.WebModule,
            services_module_1.ServicesModule
        ],
        controllers: [],
        providers: [
            {
                provide: core_1.APP_FILTER,
                useClass: http_exception_filter_1.HttpExceptionFilter
            }
        ],
    })
], SalesServiceModule);
//# sourceMappingURL=sales-service.module.js.map