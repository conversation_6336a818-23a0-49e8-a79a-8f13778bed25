import { ApiResponsePresenter } from './api-response.presenter';

/**
 * Interfaz para datos paginados
 */
export interface PaginatedData<T> {
  items: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Presentador para respuestas paginadas
 */
export class PaginatedResponsePresenter<T> extends ApiResponsePresenter<PaginatedData<T>> {
  /**
   * Crea una respuesta paginada
   */
  static fromPagination<T>(
    items: T[],
    total: number,
    page: number,
    limit: number,
    message: string = 'Datos recuperados exitosamente'
  ): PaginatedResponsePresenter<T> {
    const totalPages = Math.ceil(total / limit);
    
    const paginatedData: PaginatedData<T> = {
      items,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    };

    return new PaginatedResponsePresenter<T>(true, message, paginatedData);
  }
}
