import { ApiResponsePresenter } from './api-response.presenter';
export interface PaginatedData<T> {
    items: T[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
}
export declare class PaginatedResponsePresenter<T> extends ApiResponsePresenter<PaginatedData<T>> {
    static fromPagination<T>(items: T[], total: number, page: number, limit: number, message?: string): PaginatedResponsePresenter<T>;
}
