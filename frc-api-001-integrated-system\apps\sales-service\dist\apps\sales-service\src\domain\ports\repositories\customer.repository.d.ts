import { Customer } from '../../models/customer';
export declare const CUSTOMER_REPOSITORY: unique symbol;
export interface ICustomerRepository {
    findAll(): Promise<Customer[]>;
    findById(id: number): Promise<Customer | null>;
    findByDoi(doi: string): Promise<Customer | null>;
    create(customer: Customer): Promise<Customer>;
    update(id: number, customer: Customer): Promise<Customer>;
    delete(id: number): Promise<void>;
}
