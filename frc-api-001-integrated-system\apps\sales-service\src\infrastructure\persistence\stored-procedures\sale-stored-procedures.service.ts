import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../../../../libs/prisma/prisma.service';
import { Sale } from '../../../domain/models/sale';
import { SaleBuilder } from '../../../domain/builders/sale.builder';
import { Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { ISaleStoredProcedures } from './sale-stored-procedures.interface';

/**
 * Servicio para ejecutar procedimientos almacenados relacionados con ventas
 * Implementa procedimientos complejos que requieren múltiples operaciones en la base de datos
 */
@Injectable()
export class SaleStoredProceduresService implements ISaleStoredProcedures {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly saleBuilder: SaleBuilder
  ) {}

  /**
   * Procedimiento para crear una venta completa con cliente y ubicación
   * @param saleData Datos de la venta
   * @param customerData Datos del cliente
   * @param locationData Datos de la ubicación (opcional)
   * @returns La venta creada como modelo de dominio
   */
  async createCompleteSale(
    saleData: {
      policy_number?: string;
      start_sale_date?: Date;
      end_sale_date?: Date;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
      account_id: number;
    },
    customerData: {
      full_name: string;
      doi?: string;
      phone_number?: string;
      mobile_number?: string;
      document_type_id: number;
    },
    locationData?: {
      address?: string;
      department?: string;
      province?: string;
      district?: string;
    }
  ): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this.prismaService.$transaction(async (prisma) => {
      // 1. Verificar si el cliente ya existe por DOI
      let customerId: number;
      
      if (customerData.doi) {
        const existingCustomer = await prisma.customer.findUnique({
          where: { doi: customerData.doi }
        });
        
        if (existingCustomer) {
          // Actualizar cliente existente
          const updatedCustomer = await prisma.customer.update({
            where: { customer_id: existingCustomer.customer_id },
            data: {
              full_name: customerData.full_name,
              phone_number: customerData.phone_number,
              mobile_number: customerData.mobile_number,
              document_type_id: customerData.document_type_id
            }
          });
          customerId = updatedCustomer.customer_id;
        } else {
          // Crear nuevo cliente
          customerId = await this.createCustomerWithLocation(prisma, customerData, locationData);
        }
      } else {
        // Crear nuevo cliente sin DOI
        customerId = await this.createCustomerWithLocation(prisma, customerData, locationData);
      }

      // 3. Crear la venta
      const sale = await prisma.sale.create({
        data: {
          policy_number: saleData.policy_number || undefined,
          start_sale_date: saleData.start_sale_date || undefined,
          end_sale_date: saleData.end_sale_date || undefined,
          net_amount: saleData.net_amount as any,
          total_amount: saleData.total_amount as any,
          condition_sale_id: saleData.condition_sale_id || undefined,
          currency_id: saleData.currency_id || undefined,
          cia_company_id: saleData.cia_company_id || undefined,
          insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id || undefined,
          customer_id: customerId,
          account_id: saleData.account_id
        },
        include: {
          Customer: true
        }
      });

      // 4. Mapear la entidad Prisma a un modelo de dominio
      return this.mapSaleToDomain(sale);
    });
  }

  /**
   * Procedimiento para actualizar una venta completa con cliente y ubicación
   * @param saleId ID de la venta a actualizar
   * @param saleData Datos de la venta
   * @param customerData Datos del cliente (opcional)
   * @param locationData Datos de la ubicación (opcional)
   * @returns La venta actualizada como modelo de dominio
   */
  async updateCompleteSale(
    saleId: number,
    saleData: {
      policy_number?: string;
      start_sale_date?: Date;
      end_sale_date?: Date;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
      account_id?: number;
    },
    customerData?: {
      full_name?: string;
      doi?: string;
      phone_number?: string;
      mobile_number?: string;
      document_type_id?: number;
    },
    locationData?: {
      address?: string;
      department?: string;
      province?: string;
      district?: string;
    }
  ): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this.prismaService.$transaction(async (prisma) => {
      // 1. Verificar que la venta existe
      const existingSale = await prisma.sale.findUnique({
        where: { sale_id: saleId },
        include: { Customer: true }
      });

      if (!existingSale) {
        throw new Error(`Venta con ID ${saleId} no encontrada`);
      }

      // 2. Actualizar cliente y ubicación si se proporcionaron datos
      if (customerData) {
        // Verificar si el cliente tiene ubicación
        if (existingSale.Customer.location_id && locationData) {
          // Actualizar ubicación existente
          await prisma.location.update({
            where: { location_id: existingSale.Customer.location_id },
            data: locationData
          });
        } else if (locationData) {
          // Crear nueva ubicación
          const location = await prisma.location.create({
            data: locationData
          });
          
          // Actualizar el cliente con la nueva ubicación
          await prisma.customer.update({
            where: { customer_id: existingSale.customer_id },
            data: {
              ...customerData,
              location_id: location.location_id
            }
          });
        } else {
          // Actualizar solo el cliente sin cambiar la ubicación
          await prisma.customer.update({
            where: { customer_id: existingSale.customer_id },
            data: customerData
          });
        }
      }

      // 3. Actualizar la venta
      const updatedSale = await prisma.sale.update({
        where: { sale_id: saleId },
        data: {
          policy_number: saleData.policy_number || undefined,
          start_sale_date: saleData.start_sale_date || undefined,
          end_sale_date: saleData.end_sale_date || undefined,
          net_amount: saleData.net_amount as any,
          total_amount: saleData.total_amount as any,
          condition_sale_id: saleData.condition_sale_id || undefined,
          currency_id: saleData.currency_id || undefined,
          cia_company_id: saleData.cia_company_id || undefined,
          insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id || undefined,
          account_id: saleData.account_id || existingSale.account_id
        },
        include: {
          Customer: true
        }
      });

      // 4. Mapear la entidad Prisma a un modelo de dominio
      return this.mapSaleToDomain(updatedSale);
    });
  }

  /**
   * Procedimiento para renovar una venta existente
   * @param originalSaleId ID de la venta original
   * @param newSaleData Datos de la nueva venta
   * @param startDate Fecha de inicio de la nueva venta
   * @param endDate Fecha de fin de la nueva venta
   * @param keepCustomerInfo Si se debe mantener la información del cliente
   * @param accountId ID de la cuenta que realiza la renovación
   * @returns La nueva venta creada como modelo de dominio
   */
  async renewSale(
    originalSaleId: number,
    newSaleData: {
      policy_number?: string;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
    },
    startDate: Date,
    endDate: Date,
    keepCustomerInfo: boolean = true,
    accountId: number
  ): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this.prismaService.$transaction(async (prisma) => {
      // 1. Verificar que la venta original existe
      const originalSale = await prisma.sale.findUnique({
        where: { sale_id: originalSaleId },
        include: { Customer: true }
      });

      if (!originalSale) {
        throw new Error(`Venta original con ID ${originalSaleId} no encontrada`);
      }

      // 2. Crear la nueva venta como renovación
      const newSale = await prisma.sale.create({
        data: {
          policy_number: newSaleData.policy_number || originalSale.policy_number || undefined,
          start_sale_date: startDate,
          end_sale_date: endDate,
          net_amount: newSaleData.net_amount as any || originalSale.net_amount,
          total_amount: newSaleData.total_amount as any || originalSale.total_amount,
          condition_sale_id: newSaleData.condition_sale_id || originalSale.condition_sale_id || undefined,
          currency_id: newSaleData.currency_id || originalSale.currency_id || undefined,
          cia_company_id: newSaleData.cia_company_id || originalSale.cia_company_id || undefined,
          insurance_line_cia_sale_id: newSaleData.insurance_line_cia_sale_id || originalSale.insurance_line_cia_sale_id || undefined,
          customer_id: originalSale.customer_id,
          account_id: accountId
        },
        include: {
          Customer: true
        }
      });

      // 3. Mapear la entidad Prisma a un modelo de dominio
      return this.mapSaleToDomain(newSale);
    });
  }

  /**
   * Procedimiento para obtener ventas con filtros avanzados
   * @param filters Filtros para la búsqueda
   * @param pagination Opciones de paginación
   * @returns Lista de ventas como modelos de dominio
   */
  async getSalesWithFilters(
    filters: {
      customerId?: number;
      customerDoi?: string;
      customerName?: string;
      policyNumber?: string;
      startDateFrom?: Date;
      startDateTo?: Date;
      endDateFrom?: Date;
      endDateTo?: Date;
      accountId?: number;
      conditionSaleId?: number;
      currencyId?: number;
      ciaCompanyId?: number;
      insuranceLineCiaSaleId?: number;
    },
    pagination: {
      skip?: number;
      take?: number;
      orderBy?: string;
      orderDirection?: 'asc' | 'desc';
    } = { skip: 0, take: 10, orderBy: 'sale_id', orderDirection: 'desc' }
  ): Promise<{ sales: Sale[]; total: number }> {
    // Construir el objeto de filtros para Prisma
    const where: any = {};

    // Filtros de cliente
    if (filters.customerId) {
      where.customer_id = filters.customerId;
    }

    // Filtros de venta
    if (filters.policyNumber) {
      where.policy_number = { contains: filters.policyNumber };
    }

    if (filters.startDateFrom || filters.startDateTo) {
      where.start_sale_date = {};
      if (filters.startDateFrom) {
        where.start_sale_date.gte = filters.startDateFrom;
      }
      if (filters.startDateTo) {
        where.start_sale_date.lte = filters.startDateTo;
      }
    }

    if (filters.endDateFrom || filters.endDateTo) {
      where.end_sale_date = {};
      if (filters.endDateFrom) {
        where.end_sale_date.gte = filters.endDateFrom;
      }
      if (filters.endDateTo) {
        where.end_sale_date.lte = filters.endDateTo;
      }
    }

    // Filtros de relaciones
    if (filters.accountId) {
      where.account_id = filters.accountId;
    }

    if (filters.conditionSaleId) {
      where.condition_sale_id = filters.conditionSaleId;
    }

    if (filters.currencyId) {
      where.currency_id = filters.currencyId;
    }

    if (filters.ciaCompanyId) {
      where.cia_company_id = filters.ciaCompanyId;
    }

    if (filters.insuranceLineCiaSaleId) {
      where.insurance_line_cia_sale_id = filters.insuranceLineCiaSaleId;
    }

    // Filtros que requieren joins
    if (filters.customerDoi || filters.customerName) {
      where.Customer = {};
      
      if (filters.customerDoi) {
        where.Customer.doi = { contains: filters.customerDoi };
      }
      
      if (filters.customerName) {
        where.Customer.full_name = { contains: filters.customerName };
      }
    }

    // Ejecutar la consulta con los filtros y paginación
    let orderByOption: any = { id: 'desc' };
    if (pagination.orderBy) {
      orderByOption = {};
      orderByOption[pagination.orderBy as string] = pagination.orderDirection || 'asc';
    }
    
    const [sales, total] = await Promise.all([
      this.prismaService.sale.findMany({
        where,
        skip: pagination.skip,
        take: pagination.take,
        orderBy: orderByOption,
        include: {
          Customer: true
        }
      }),
      this.prismaService.sale.count({ where })
    ]);

    // Mapear los resultados a modelos de dominio
    return {
      sales: sales.map(sale => this.mapSaleToDomain(sale)),
      total
    };
  }

  /**
   * Método auxiliar para crear un cliente con ubicación opcional
   * @param prisma Instancia de transacción Prisma
   * @param customerData Datos del cliente
   * @param locationData Datos de la ubicación (opcional)
   * @returns ID del cliente creado
   */
  private async createCustomerWithLocation(
    prisma: any,
    customerData: {
      full_name: string;
      doi?: string;
      phone_number?: string;
      mobile_number?: string;
      document_type_id: number;
    },
    locationData?: {
      address?: string;
      department?: string;
      province?: string;
      district?: string;
    }
  ): Promise<number> {
    // Si se proporcionaron datos de ubicación, crear primero la ubicación
    let locationId: number | undefined = undefined;
    
    if (locationData) {
      const location = await prisma.location.create({
        data: locationData
      });
      locationId = location.location_id;
    }

    // Crear el cliente con referencia a la ubicación si existe
    const customer = await prisma.customer.create({
      data: {
        full_name: customerData.full_name,
        doi: customerData.doi,
        phone_number: customerData.phone_number,
        mobile_number: customerData.mobile_number,
        document_type_id: customerData.document_type_id,
        location_id: locationId
      }
    });

    return customer.customer_id;
  }

  /**
   * Método auxiliar para mapear una entidad Sale de Prisma a un modelo de dominio
   * @param sale Entidad Sale de Prisma
   * @returns Modelo de dominio Sale
   */
  private mapSaleToDomain(sale: any): Sale {
    return this.saleBuilder
      .withSaleId(sale.sale_id)
      .withPolicyNumber(sale.policy_number || undefined)
      .withStartSaleDate(sale.start_sale_date || undefined)
      .withEndSaleDate(sale.end_sale_date || undefined)
      .withNetAmount(Number(sale.net_amount) || 0)
      .withTotalAmount(Number(sale.total_amount) || 0)
      .withConditionSaleId(sale.condition_sale_id || undefined)
      .withCurrencyId(sale.currency_id || undefined)
      .withCiaCompanyId(sale.cia_company_id || undefined)
      .withInsuranceLineCiaSaleId(sale.insurance_line_cia_sale_id || undefined)
      .withCustomerId(sale.customer_id)
      .withAccountId(sale.account_id)
      .build();
  }
}
