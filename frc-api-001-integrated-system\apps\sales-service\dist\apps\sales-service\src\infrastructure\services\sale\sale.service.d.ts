import { Sale } from '../../../domain/models/sale';
import { CreateSaleDto } from '../../../domain/ports/transactions/dtos/create-sale.dto';
import { UpdateSaleDto } from '../../../domain/ports/transactions/dtos/update-sale.dto';
import { ISaleStoredProcedures } from '../../persistence/stored-procedures/sale-stored-procedures.interface';
import { PrismaService } from '../../../../../../libs/prisma/prisma.service';
import { SaleBuilder } from '../../../domain/builders/sale.builder';
export declare class SaleService {
    private readonly saleStoredProcedures;
    private readonly prismaService;
    private readonly saleBuilder;
    constructor(saleStoredProcedures: ISaleStoredProcedures, prismaService: PrismaService, saleBuilder: SaleBuilder);
    createSale(createSaleDto: CreateSaleDto): Promise<Sale>;
    updateSale(updateSaleDto: UpdateSaleDto): Promise<Sale>;
    renewSale(originalSaleId: number, newSaleData: {
        policy_number?: string;
        net_amount?: number;
        total_amount?: number;
        condition_sale_id?: number;
        currency_id?: number;
        cia_company_id?: number;
        insurance_line_cia_sale_id?: number;
    }, startDate: Date, endDate: Date, keepCustomerInfo: boolean, accountId: number): Promise<Sale>;
    getSaleById(id: number): Promise<Sale | null>;
    getSalesWithFilters(filters: {
        customerId?: number;
        customerDoi?: string;
        customerName?: string;
        policyNumber?: string;
        startDateFrom?: Date;
        startDateTo?: Date;
        endDateFrom?: Date;
        endDateTo?: Date;
        accountId?: number;
        conditionSaleId?: number;
        currencyId?: number;
        ciaCompanyId?: number;
        insuranceLineCiaSaleId?: number;
    }, pagination?: {
        skip?: number;
        take?: number;
        orderBy?: string;
        orderDirection?: 'asc' | 'desc';
    }): Promise<{
        sales: Sale[];
        total: number;
    }>;
    deleteSale(id: number): Promise<boolean>;
}
