import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { ICustomerRepository } from "../../../domain/ports/repositories/customer.repository";
import { Customer } from "../../../domain/models/customer";
export declare class PrismaCustomerRepository implements ICustomerRepository {
    private readonly _prismaService;
    private readonly customerBuilder;
    constructor(_prismaService: PrismaService);
    findAll(): Promise<Customer[]>;
    findById(id: number): Promise<Customer | null>;
    findByDoi(doi: string): Promise<Customer | null>;
    create(customer: Customer): Promise<Customer>;
    update(id: number, customer: Customer): Promise<Customer>;
    delete(id: number): Promise<void>;
    private mapPrismaToCustomer;
}
