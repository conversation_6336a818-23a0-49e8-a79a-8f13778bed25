import { Location } from '../../models/location';
export declare const LOCATION_REPOSITORY: unique symbol;
export interface ILocationRepository {
    findAll(): Promise<Location[]>;
    findById(id: number): Promise<Location | null>;
    create(location: Location): Promise<Location>;
    update(id: number, location: Location): Promise<Location>;
    delete(id: number): Promise<void>;
    getAllDepartments(): Promise<string[]>;
    getProvincesByDepartment(department: string): Promise<string[]>;
    getDistrictsByProvince(province: string): Promise<string[]>;
}
