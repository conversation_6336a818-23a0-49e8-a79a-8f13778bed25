import { Customer } from '../../models/customer';

export const CUSTOMER_REPOSITORY = Symbol('CUSTOMER_REPOSITORY');
export interface ICustomerRepository {
  findAll(): Promise<Customer[]>;
  findById(id: number): Promise<Customer | null>;
  findByDoi(doi: string): Promise<Customer | null>;
  create(customer: Customer): Promise<Customer>;
  update(id: number, customer: Customer): Promise<Customer>;
  delete(id: number): Promise<void>;
}
