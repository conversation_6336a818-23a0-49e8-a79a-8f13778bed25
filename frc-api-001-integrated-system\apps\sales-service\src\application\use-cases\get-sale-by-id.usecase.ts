import { Inject } from "@nestjs/common";
import { SALE_REPOSITORY, ISaleRepository } from "../../domain/ports/repositories/sale.repository";
import { CUSTOMER_REPOSITORY, ICustomerRepository } from "../../domain/ports/repositories/customer.repository";
import { LOCATION_REPOSITORY, ILocationRepository } from "../../domain/ports/repositories/location.repository";
import { Sale } from "../../domain/models/sale";
import { Customer } from "../../domain/models/customer";
import { Location } from "../../domain/models/location";

export const GET_SALE_BY_ID_USE_CASE = Symbol('GET_SALE_BY_ID_USE_CASE');
export class GetSaleByIdUseCase {
  constructor(
    @Inject(SALE_REPOSITORY)
    private readonly _saleRepository: ISaleRepository,
    @Inject(CUSTOMER_REPOSITORY)
    private readonly _customerRepository: ICustomerRepository,
    @Inject(LOCATION_REPOSITORY)
    private readonly _locationRepository: ILocationRepository
  ) {}

  async execute(saleId: number): Promise<{ sale: Sale; customer: Customer; location: Location | null }> {
    // Obtener la venta por ID
    const sale = await this._saleRepository.findById(saleId);
    
    if (!sale) {
      throw new Error(`Venta con ID ${saleId} no encontrada`);
    }
    
    // Obtener el cliente asociado a la venta
    const customer = await this._customerRepository.findById(sale.customer_id);
    
    if (!customer) {
      throw new Error(`Cliente con ID ${sale.customer_id} no encontrado`);
    }
    
    // Obtener la ubicación asociada al cliente
    let location: Location | null = null;
    if (customer.location_id) {
      location = await this._locationRepository.findById(customer.location_id);
    }
    
    return {
      sale,
      customer,
      location
    };
  }
}
