"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaCreateSaleTransaction = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../../../../libs/prisma/prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let PrismaCreateSaleTransaction = class PrismaCreateSaleTransaction {
    _prismaService;
    saleBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
    }
    async run(createSaleDto) {
        return this._prismaService.$transaction(async (prisma) => {
            let existingCustomer = null;
            if (createSaleDto.customer.doi) {
                existingCustomer = await prisma.customer.findUnique({
                    where: { doi: createSaleDto.customer.doi }
                });
            }
            let locationId = undefined;
            if (createSaleDto.location) {
                const locationData = {
                    address: createSaleDto.location.address,
                    department: createSaleDto.location.department,
                    province: createSaleDto.location.province,
                    district: createSaleDto.location.district
                };
                const location = await prisma.location.create({
                    data: locationData
                });
                locationId = location.location_id;
            }
            let customer;
            if (existingCustomer) {
                customer = await prisma.customer.update({
                    where: { customer_id: existingCustomer.customer_id },
                    data: {
                        full_name: createSaleDto.customer.full_name,
                        phone_number: createSaleDto.customer.phone_number,
                        mobile_number: createSaleDto.customer.mobile_number,
                        document_type_id: createSaleDto.customer.document_type_id,
                        location_id: locationId || existingCustomer.location_id
                    }
                });
            }
            else {
                customer = await prisma.customer.create({
                    data: {
                        full_name: createSaleDto.customer.full_name,
                        doi: createSaleDto.customer.doi,
                        phone_number: createSaleDto.customer.phone_number,
                        mobile_number: createSaleDto.customer.mobile_number,
                        document_type_id: createSaleDto.customer.document_type_id,
                        location_id: locationId
                    }
                });
            }
            const sale = await prisma.sale.create({
                data: {
                    policy_number: createSaleDto.sale.policy_number || undefined,
                    start_sale_date: createSaleDto.sale.start_sale_date || undefined,
                    end_sale_date: createSaleDto.sale.end_sale_date || undefined,
                    net_amount: createSaleDto.sale.net_amount,
                    total_amount: createSaleDto.sale.total_amount,
                    condition_sale_id: createSaleDto.sale.condition_sale_id || undefined,
                    currency_id: createSaleDto.sale.currency_id || undefined,
                    cia_company_id: createSaleDto.sale.cia_company_id || undefined,
                    insurance_line_cia_sale_id: createSaleDto.sale.insurance_line_cia_sale_id || undefined,
                    customer_id: customer.customer_id,
                    account_id: createSaleDto.accountId
                }
            });
            return this.saleBuilder
                .withSaleId(sale.sale_id)
                .withPolicyNumber(sale.policy_number || undefined)
                .withStartSaleDate(sale.start_sale_date || undefined)
                .withEndSaleDate(sale.end_sale_date || undefined)
                .withNetAmount(Number(sale.net_amount) || 0)
                .withTotalAmount(Number(sale.total_amount) || 0)
                .withConditionSaleId(sale.condition_sale_id || undefined)
                .withCurrencyId(sale.currency_id || undefined)
                .withCiaCompanyId(sale.cia_company_id || undefined)
                .withInsuranceLineCiaSaleId(sale.insurance_line_cia_sale_id || undefined)
                .withCustomerId(sale.customer_id)
                .withAccountId(sale.account_id)
                .build();
        });
    }
};
exports.PrismaCreateSaleTransaction = PrismaCreateSaleTransaction;
exports.PrismaCreateSaleTransaction = PrismaCreateSaleTransaction = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PrismaCreateSaleTransaction);
//# sourceMappingURL=prisma-create-sale.transaction.js.map