import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { IDeleteSaleTransaction, DELETE_SALE_TRANSACTION } from "../../../domain/ports/transactions/delete-sale.transaction";
import { DeleteSaleDto } from "../../../domain/ports/transactions/dtos/delete-sale.dto";

@Injectable()
export class PrismaDeleteSaleTransaction implements IDeleteSaleTransaction {
  constructor(
    private readonly _prismaService: PrismaService,
  ) {}

  async run(deleteSaleDto: DeleteSaleDto): Promise<void> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    await this._prismaService.$transaction(async (prisma) => {
      // 1. Verificar que la venta existe
      const existingSale = await prisma.sale.findUnique({
        where: { sale_id: deleteSaleDto.saleId }
      });

      if (!existingSale) {
        throw new Error(`Venta con ID ${deleteSaleDto.saleId} no encontrada`);
      }

      // 2. Eliminar la venta
      // Nota: Como no hay campo is_deleted en el esquema de Prisma,
      // siempre realizamos una eliminación física
      await prisma.sale.delete({
        where: { sale_id: deleteSaleDto.saleId }
      });

      // No es necesario eliminar el cliente ni la ubicación, ya que pueden
      // estar asociados a otras ventas
    });
  }
}
