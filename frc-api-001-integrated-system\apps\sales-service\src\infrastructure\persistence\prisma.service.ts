import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '../../../../../generated/prisma';

/**
 * Servicio de Prisma específico para el sales-service
 * Utiliza el cliente de Prisma generado localmente para este servicio
 */
@Injectable()
export class SalesServicePrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
