"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaCustomerRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const customer_builder_1 = require("../../../domain/builders/customer.builder");
let PrismaCustomerRepository = class PrismaCustomerRepository {
    _prismaService;
    customerBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.customerBuilder = new customer_builder_1.CustomerBuilder();
    }
    async findAll() {
        const customers = await this._prismaService.customer.findMany();
        return customers.map(customer => this.mapPrismaToCustomer(customer));
    }
    async findById(id) {
        const customer = await this._prismaService.customer.findUnique({
            where: { customer_id: id }
        });
        if (!customer)
            return null;
        return this.mapPrismaToCustomer(customer);
    }
    async findByDoi(doi) {
        const customer = await this._prismaService.customer.findFirst({
            where: { doi }
        });
        if (!customer)
            return null;
        return this.mapPrismaToCustomer(customer);
    }
    async create(customer) {
        const createdCustomer = await this._prismaService.customer.create({
            data: {
                full_name: customer.full_name,
                doi: customer.doi,
                phone_number: customer.phone_number,
                mobile_number: customer.mobile_number,
                document_type_id: customer.document_type_id
            }
        });
        return this.mapPrismaToCustomer(createdCustomer);
    }
    async update(id, customer) {
        const updatedCustomer = await this._prismaService.customer.update({
            where: { customer_id: id },
            data: {
                full_name: customer.full_name,
                doi: customer.doi,
                phone_number: customer.phone_number,
                mobile_number: customer.mobile_number,
                document_type_id: customer.document_type_id
            }
        });
        return this.mapPrismaToCustomer(updatedCustomer);
    }
    async delete(id) {
        await this._prismaService.customer.delete({
            where: { customer_id: id }
        });
    }
    mapPrismaToCustomer(prismaEntity) {
        return this.customerBuilder
            .withCustomerId(prismaEntity.customer_id)
            .withFullName(prismaEntity.full_name)
            .withDoi(prismaEntity.doi)
            .withPhoneNumber(prismaEntity.phone_number)
            .withMobileNumber(prismaEntity.mobile_number)
            .withDocumentTypeId(prismaEntity.document_type_id)
            .build();
    }
};
exports.PrismaCustomerRepository = PrismaCustomerRepository;
exports.PrismaCustomerRepository = PrismaCustomerRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaCustomerRepository);
//# sourceMappingURL=prisma-customer.repository.js.map