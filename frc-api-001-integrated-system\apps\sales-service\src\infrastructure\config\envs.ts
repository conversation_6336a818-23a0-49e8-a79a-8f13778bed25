import { z } from 'zod';

/**
 * Interfaz que define las variables de entorno necesarias para el servicio de ventas
 */
interface EnvVars {
  salesServiceHost: string;
  salesServicePort: number;
  jwtAccessSecret: string;
  secretWord: string;
}

/**
 * Esquema de validación para las variables de entorno
 */
const envSchema = z.object({
  SALES_MICROSERVICE_HOST: z.string().nonempty(),
  SALES_MICROSERVICE_PORT: z.coerce.number(),
  JWT_ACCESS_SECRET: z.string().nonempty(),
  SALES_SECRET_WORD: z.string().nonempty(),
});

/**
 * Validación de las variables de entorno
 */
console.log('Environment variables check:');
console.log('SALES_MICROSERVICE_HOST:', process.env.SALES_MICROSERVICE_HOST);
console.log('SALES_MICROSERVICE_PORT:', process.env.SALES_MICROSERVICE_PORT);
console.log('JWT_ACCESS_SECRET:', process.env.JWT_ACCESS_SECRET);
console.log('SALES_SECRET_WORD:', process.env.SALES_SECRET_WORD);

const { data, success } = envSchema.safeParse(process.env);

if (!success) {
  console.error('Validation errors:', envSchema.safeParse(process.env).error);
  throw new Error('Invalid environment variables for sales service');
}

/**
 * Exportación de las variables de entorno validadas
 */
export const envs: EnvVars = {
  salesServiceHost: data.SALES_MICROSERVICE_HOST,
  salesServicePort: data.SALES_MICROSERVICE_PORT,
  jwtAccessSecret: data.JWT_ACCESS_SECRET,
  secretWord: data.SALES_SECRET_WORD,
};
