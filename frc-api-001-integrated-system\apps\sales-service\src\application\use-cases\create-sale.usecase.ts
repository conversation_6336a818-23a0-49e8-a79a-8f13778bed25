import { Inject } from "@nestjs/common";
import { CREATE_SALE_TRANSACTION, ICreateSaleTransaction } from "../../domain/ports/transactions/create-sale.transaction";
import { CreateSaleDto } from "../../domain/ports/transactions/dtos/create-sale.dto";
import { Sale } from "../../domain/models/sale";
import { SaleBuilder } from "../../domain/builders/sale.builder";
import { CustomerBuilder } from "../../domain/builders/customer.builder";
import { LocationBuilder } from "../../domain/builders/location.builder";
import { JWT_SERVICE, IJwtService } from "../../domain/ports/services/jwt.service";

export const CREATE_SALE_USE_CASE = Symbol('CREATE_SALE_USE_CASE');
export class CreateSaleUseCase {
  private readonly saleBuilder: SaleBuilder;
  private readonly customerBuilder: CustomerBuilder;
  private readonly locationBuilder: LocationBuilder;

  constructor(
    @Inject(CREATE_SALE_TRANSACTION)
    private readonly _createSaleTransaction: ICreateSaleTransaction,
    @Inject(JWT_SERVICE)
    private readonly _jwtService: IJwtService
  ) {
    this.saleBuilder = new SaleBuilder();
    this.customerBuilder = new CustomerBuilder();
    this.locationBuilder = new LocationBuilder();
  }

  async execute(saleData: any, token: string): Promise<Sale> {
    // Extraer la información del usuario del token JWT
    const userInfo = await this._jwtService.verify(token);
    
    // Construir el objeto Sale
    const sale = this.saleBuilder
      .withPolicyNumber(saleData.policyNumber)
      .withStartSaleDate(new Date(saleData.startSaleDate))
      .withEndSaleDate(new Date(saleData.endSaleDate))
      .withNetAmount(saleData.netAmount)
      .withTotalAmount(saleData.totalAmount)
      .withAccountId(userInfo.accountId)
      .withConditionSaleId(saleData.conditionSaleId)
      .withCurrencyId(saleData.currencyId)
      .withCiaCompanyId(saleData.ciaCompanyId)
      .withInsuranceLineCiaSaleId(saleData.insuranceLineCiaSaleId)
      .build();
    
    // Construir el objeto Customer
    const customer = this.customerBuilder
      .withFullName(saleData.customer.fullName)
      .withDoi(saleData.customer.doi)
      .withPhoneNumber(saleData.customer.phoneNumber)
      .withMobileNumber(saleData.customer.mobileNumber)
      .withDocumentTypeId(saleData.customer.documentTypeId)
      .build();
    
    // Construir el objeto Location
    const location = this.locationBuilder
      .withAddress(saleData.location.address)
      .withDepartment(saleData.location.department)
      .withProvince(saleData.location.province)
      .withDistrict(saleData.location.district)
      .build();
    
    // Crear el DTO para la transacción
    const createSaleDto: CreateSaleDto = {
      sale,
      customer,
      location,
      accountId: userInfo.accountId
    };
    
    // Ejecutar la transacción
    return await this._createSaleTransaction.run(createSaleDto);
  }
}
