import { Location } from '../../models/location';

export const LOCATION_REPOSITORY = Symbol('LOCATION_REPOSITORY');
export interface ILocationRepository {
  findAll(): Promise<Location[]>;
  findById(id: number): Promise<Location | null>;
  create(location: Location): Promise<Location>;
  update(id: number, location: Location): Promise<Location>;
  delete(id: number): Promise<void>;
  
  // Métodos específicos para obtener listas de ubicaciones
  getAllDepartments(): Promise<string[]>;
  getProvincesByDepartment(department: string): Promise<string[]>;
  getDistrictsByProvince(province: string): Promise<string[]>;
}
