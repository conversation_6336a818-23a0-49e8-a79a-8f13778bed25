import { Request } from 'express';
import { AuthService } from '../../services/auth/auth.service';
import { CreateSaleUseCase } from '../../../application/use-cases/create-sale.usecase';
import { UpdateSaleUseCase } from '../../../application/use-cases/update-sale.usecase';
import { GetSaleByIdUseCase } from '../../../application/use-cases/get-sale-by-id.usecase';
import { GetSalesByPageUseCase } from '../../../application/use-cases/get-sales-by-page.usecase';
import { RenewSaleUseCase } from '../../../application/use-cases/renew-sale.usecase';
import { DeleteSaleUseCase } from '../../../application/use-cases/delete-sale.usecase';
import { CreateSaleRequestDto } from './dtos/create-sale.request.dto';
import { UpdateSaleRequestDto } from './dtos/update-sale.request.dto';
import { FilterSalesRequestDto } from './dtos/filter-sales.request.dto';
import { ApiResponsePresenter } from './presenters/api-response.presenter';
import { PaginatedResponsePresenter } from './presenters/paginated-response.presenter';
import { Sale } from '../../../domain/models/sale';
export declare class SaleController {
    private readonly createSaleUseCase;
    private readonly updateSaleUseCase;
    private readonly getSaleByIdUseCase;
    private readonly getSalesByPageUseCase;
    private readonly renewSaleUseCase;
    private readonly deleteSaleUseCase;
    private readonly authService;
    constructor(createSaleUseCase: CreateSaleUseCase, updateSaleUseCase: UpdateSaleUseCase, getSaleByIdUseCase: GetSaleByIdUseCase, getSalesByPageUseCase: GetSalesByPageUseCase, renewSaleUseCase: RenewSaleUseCase, deleteSaleUseCase: DeleteSaleUseCase, authService: AuthService);
    createSale(createSaleRequestDto: CreateSaleRequestDto, request: Request): Promise<ApiResponsePresenter<Sale>>;
    updateSale(id: number, updateSaleRequestDto: UpdateSaleRequestDto, request: Request): Promise<ApiResponsePresenter<Sale>>;
    renewSale(id: number, request: Request): Promise<ApiResponsePresenter<Sale>>;
    getSaleById(id: number, request: Request): Promise<ApiResponsePresenter<Sale>>;
    getSalesWithFilters(filterDto: FilterSalesRequestDto, request: Request): Promise<PaginatedResponsePresenter<Sale>>;
    deleteSale(id: number, request: Request): Promise<ApiResponsePresenter<void>>;
}
