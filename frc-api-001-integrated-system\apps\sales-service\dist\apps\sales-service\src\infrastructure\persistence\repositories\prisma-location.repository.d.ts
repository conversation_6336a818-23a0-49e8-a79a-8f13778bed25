import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { ILocationRepository } from "../../../domain/ports/repositories/location.repository";
import { Location } from "../../../domain/models/location";
export declare class PrismaLocationRepository implements ILocationRepository {
    private readonly _prismaService;
    private readonly locationBuilder;
    constructor(_prismaService: PrismaService);
    findAll(): Promise<Location[]>;
    findById(id: number): Promise<Location | null>;
    create(location: Location): Promise<Location>;
    update(id: number, location: Location): Promise<Location>;
    delete(id: number): Promise<void>;
    getAllDepartments(): Promise<string[]>;
    getProvincesByDepartment(department: string): Promise<string[]>;
    getDistrictsByProvince(province: string): Promise<string[]>;
    private mapPrismaToLocation;
}
