import { Module, Global } from '@nestjs/common';
import { envs } from './envs';
import { ConfigService } from './config.service';

/**
 * Módulo de configuración para el servicio de ventas
 * Permite la carga y validación de variables de entorno
 */
@Global()
@Module({
  providers: [
    {
      provide: 'CONFIG',
      useValue: envs
    },
    ConfigService
  ],
  exports: ['CONFIG', ConfigService],
})
export class ConfigModule {}
