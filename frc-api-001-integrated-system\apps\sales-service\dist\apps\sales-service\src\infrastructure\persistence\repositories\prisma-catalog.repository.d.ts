import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { ICatalogRepository } from "../../../domain/ports/repositories/catalog.repository";
import { CiaCompany } from "../../../domain/models/cia-company";
import { ConditionSale } from "../../../domain/models/condition-sale";
import { Currency } from "../../../domain/models/currency";
import { DocumentType } from "../../../domain/models/document-type";
import { InsuranceLineCiaSale } from "../../../domain/models/insurance-line-cia-sale";
export declare class PrismaCatalogRepository implements ICatalogRepository {
    private readonly _prismaService;
    constructor(_prismaService: PrismaService);
    getAllCiaCompanies(): Promise<CiaCompany[]>;
    getCiaCompanyById(id: number): Promise<CiaCompany | null>;
    getAllConditionSales(): Promise<ConditionSale[]>;
    getConditionSaleById(id: number): Promise<ConditionSale | null>;
    getAllCurrencies(): Promise<Currency[]>;
    getCurrencyById(id: number): Promise<Currency | null>;
    getAllDocumentTypes(): Promise<DocumentType[]>;
    getDocumentTypeById(id: number): Promise<DocumentType | null>;
    getAllInsuranceLineCiaSales(): Promise<InsuranceLineCiaSale[]>;
    getInsuranceLineCiaSaleById(id: number): Promise<InsuranceLineCiaSale | null>;
}
