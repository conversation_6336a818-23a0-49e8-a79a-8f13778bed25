{"version": 3, "file": "sale-stored-procedures.service.js", "sourceRoot": "", "sources": ["../../../../../../../src/infrastructure/persistence/stored-procedures/sale-stored-procedures.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,sDAA8D;AAE9D,wEAAoE;AAU7D,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAEnB;IACA;IAFnB,YACmB,aAAwC,EACxC,WAAwB;QADxB,kBAAa,GAAb,aAAa,CAA2B;QACxC,gBAAW,GAAX,WAAW,CAAa;IACxC,CAAC;IASJ,KAAK,CAAC,kBAAkB,CACtB,QAWC,EACD,YAMC,EACD,YAKC;QAGD,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAEtD,IAAI,UAAkB,CAAC;YAEvB,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;gBACrB,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACxD,KAAK,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE;iBACjC,CAAC,CAAC;gBAEH,IAAI,gBAAgB,EAAE,CAAC;oBAErB,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBACnD,KAAK,EAAE,EAAE,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE;wBACpD,IAAI,EAAE;4BACJ,SAAS,EAAE,YAAY,CAAC,SAAS;4BACjC,YAAY,EAAE,YAAY,CAAC,YAAY;4BACvC,aAAa,EAAE,YAAY,CAAC,aAAa;4BACzC,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;yBAChD;qBACF,CAAC,CAAC;oBACH,UAAU,GAAG,eAAe,CAAC,WAAW,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBAEN,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;gBACzF,CAAC;YACH,CAAC;iBAAM,CAAC;gBAEN,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YACzF,CAAC;YAGD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAI,EAAE;oBACJ,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,SAAS;oBAClD,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,SAAS;oBACtD,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,SAAS;oBAClD,UAAU,EAAE,QAAQ,CAAC,UAAiB;oBACtC,YAAY,EAAE,QAAQ,CAAC,YAAmB;oBAC1C,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,IAAI,SAAS;oBAC1D,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,SAAS;oBAC9C,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,SAAS;oBACpD,0BAA0B,EAAE,QAAQ,CAAC,0BAA0B,IAAI,SAAS;oBAC5E,WAAW,EAAE,UAAU;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAGH,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAUD,KAAK,CAAC,kBAAkB,CACtB,MAAc,EACd,QAWC,EACD,YAMC,EACD,YAKC;QAGD,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAEtD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,gBAAgB,MAAM,gBAAgB,CAAC,CAAC;YAC1D,CAAC;YAGD,IAAI,YAAY,EAAE,CAAC;gBAEjB,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,IAAI,YAAY,EAAE,CAAC;oBAEtD,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC3B,KAAK,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE;wBACzD,IAAI,EAAE,YAAY;qBACnB,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,YAAY,EAAE,CAAC;oBAExB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC5C,IAAI,EAAE,YAAY;qBACnB,CAAC,CAAC;oBAGH,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC3B,KAAK,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,WAAW,EAAE;wBAChD,IAAI,EAAE;4BACJ,GAAG,YAAY;4BACf,WAAW,EAAE,QAAQ,CAAC,WAAW;yBAClC;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBAEN,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC3B,KAAK,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,WAAW,EAAE;wBAChD,IAAI,EAAE,YAAY;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC3C,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,IAAI,EAAE;oBACJ,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,SAAS;oBAClD,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,SAAS;oBACtD,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,SAAS;oBAClD,UAAU,EAAE,QAAQ,CAAC,UAAiB;oBACtC,YAAY,EAAE,QAAQ,CAAC,YAAmB;oBAC1C,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,IAAI,SAAS;oBAC1D,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,SAAS;oBAC9C,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,SAAS;oBACpD,0BAA0B,EAAE,QAAQ,CAAC,0BAA0B,IAAI,SAAS;oBAC5E,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU;iBAC3D;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAGH,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAYD,KAAK,CAAC,SAAS,CACb,cAAsB,EACtB,WAQC,EACD,SAAe,EACf,OAAa,EACb,mBAA4B,IAAI,EAChC,SAAiB;QAGjB,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAEtD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,KAAK,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE;gBAClC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,cAAc,gBAAgB,CAAC,CAAC;YAC3E,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvC,IAAI,EAAE;oBACJ,aAAa,EAAE,WAAW,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,IAAI,SAAS;oBACnF,eAAe,EAAE,SAAS;oBAC1B,aAAa,EAAE,OAAO;oBACtB,UAAU,EAAE,WAAW,CAAC,UAAiB,IAAI,YAAY,CAAC,UAAU;oBACpE,YAAY,EAAE,WAAW,CAAC,YAAmB,IAAI,YAAY,CAAC,YAAY;oBAC1E,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,IAAI,YAAY,CAAC,iBAAiB,IAAI,SAAS;oBAC/F,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,YAAY,CAAC,WAAW,IAAI,SAAS;oBAC7E,cAAc,EAAE,WAAW,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc,IAAI,SAAS;oBACtF,0BAA0B,EAAE,WAAW,CAAC,0BAA0B,IAAI,YAAY,CAAC,0BAA0B,IAAI,SAAS;oBAC1H,WAAW,EAAE,YAAY,CAAC,WAAW;oBACrC,UAAU,EAAE,SAAS;iBACtB;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAGH,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAQD,KAAK,CAAC,mBAAmB,CACvB,OAcC,EACD,aAKI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;QAGrE,MAAM,KAAK,GAAQ,EAAE,CAAC;QAGtB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QACzC,CAAC;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,CAAC,aAAa,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC;QAC3D,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACjD,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;YAC3B,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,KAAK,CAAC,eAAe,CAAC,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC;YACpD,CAAC;YACD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,KAAK,CAAC,eAAe,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;YACzB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,KAAK,CAAC,aAAa,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC;YAChD,CAAC;YACD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,KAAK,CAAC,aAAa,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;YAC9C,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC;QAED,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACnC,KAAK,CAAC,0BAA0B,GAAG,OAAO,CAAC,sBAAsB,CAAC;QACpE,CAAC;QAGD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAChD,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YAEpB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC;YACzD,CAAC;YAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC;YAChE,CAAC;QACH,CAAC;QAGD,IAAI,aAAa,GAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;QACxC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,aAAa,GAAG,EAAE,CAAC;YACnB,aAAa,CAAC,UAAU,CAAC,OAAiB,CAAC,GAAG,UAAU,CAAC,cAAc,IAAI,KAAK,CAAC;QACnF,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC/B,KAAK;gBACL,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,OAAO,EAAE,aAAa;gBACtB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SACzC,CAAC,CAAC;QAGH,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACpD,KAAK;SACN,CAAC;IACJ,CAAC;IASO,KAAK,CAAC,0BAA0B,CACtC,MAAW,EACX,YAMC,EACD,YAKC;QAGD,IAAI,UAAU,GAAuB,SAAS,CAAC;QAE/C,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE,YAAY;aACnB,CAAC,CAAC;YACH,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC;QACpC,CAAC;QAGD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,IAAI,EAAE;gBACJ,SAAS,EAAE,YAAY,CAAC,SAAS;gBACjC,GAAG,EAAE,YAAY,CAAC,GAAG;gBACrB,YAAY,EAAE,YAAY,CAAC,YAAY;gBACvC,aAAa,EAAE,YAAY,CAAC,aAAa;gBACzC,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;gBAC/C,WAAW,EAAE,UAAU;aACxB;SACF,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,WAAW,CAAC;IAC9B,CAAC;IAOO,eAAe,CAAC,IAAS;QAC/B,OAAO,IAAI,CAAC,WAAW;aACpB,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;aACxB,gBAAgB,CAAC,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;aACjD,iBAAiB,CAAC,IAAI,CAAC,eAAe,IAAI,SAAS,CAAC;aACpD,eAAe,CAAC,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;aAChD,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aAC3C,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;aAC/C,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,IAAI,SAAS,CAAC;aACxD,cAAc,CAAC,IAAI,CAAC,WAAW,IAAI,SAAS,CAAC;aAC7C,gBAAgB,CAAC,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;aAClD,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,IAAI,SAAS,CAAC;aACxE,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;aAChC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;aAC9B,KAAK,EAAE,CAAC;IACb,CAAC;CACF,CAAA;AAxcY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAGuB,0CAAyB;QAC3B,0BAAW;GAHhC,2BAA2B,CAwcvC"}