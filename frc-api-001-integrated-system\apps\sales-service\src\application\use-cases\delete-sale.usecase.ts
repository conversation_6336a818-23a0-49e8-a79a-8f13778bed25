import { Inject } from "@nestjs/common";
import { DELETE_SALE_TRANSACTION, IDeleteSaleTransaction } from "../../domain/ports/transactions/delete-sale.transaction";
import { DeleteSaleDto } from "../../domain/ports/transactions/dtos/delete-sale.dto";
import { JWT_SERVICE, IJwtService } from "../../domain/ports/services/jwt.service";

export const DELETE_SALE_USE_CASE = Symbol('DELETE_SALE_USE_CASE');
export class DeleteSaleUseCase {
  constructor(
    @Inject(DELETE_SALE_TRANSACTION)
    private readonly _deleteSaleTransaction: IDeleteSaleTransaction,
    @Inject(JWT_SERVICE)
    private readonly _jwtService: IJwtService
  ) {}

  async execute(saleId: number, hardDelete: boolean, token: string): Promise<void> {
    // Extraer la información del usuario del token JWT
    const userInfo = await this._jwtService.verify(token);
    
    // Crear el DTO para la transacción
    const deleteSaleDto: DeleteSaleDto = {
      saleId,
      accountId: userInfo.accountId,
      hardDelete
    };
    
    // Ejecutar la transacción
    await this._deleteSaleTransaction.run(deleteSaleDto);
  }
}
