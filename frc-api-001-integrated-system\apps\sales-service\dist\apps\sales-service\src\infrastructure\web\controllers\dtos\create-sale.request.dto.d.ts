export declare class CreateCustomerDto {
    full_name: string;
    doi: string;
    phone_number?: string;
    mobile_number?: string;
    document_type_id: number;
}
export declare class CreateLocationDto {
    address: string;
    department: string;
    province: string;
    district: string;
}
export declare class CreateSaleInfoDto {
    policy_number?: string;
    start_sale_date?: Date;
    end_sale_date?: Date;
    net_amount?: number;
    total_amount?: number;
    condition_sale_id?: number;
    currency_id?: number;
    cia_company_id?: number;
    insurance_line_cia_sale_id?: number;
}
export declare class CreateSaleRequestDto {
    sale: CreateSaleInfoDto;
    customer: CreateCustomerDto;
    location?: CreateLocationDto;
}
