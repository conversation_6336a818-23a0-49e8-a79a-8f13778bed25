import { Customer } from '../../../models/customer';
import { Location } from '../../../models/location';
import { Sale } from '../../../models/sale';

export class UpdateSaleDto {
  saleId: number;           // ID de la venta a actualizar
  sale: Partial<Sale>;      // Datos de la venta a actualizar
  customer?: Partial<Customer>; // Datos del cliente a actualizar (opcional)
  location?: Partial<Location>; // Datos de la ubicación a actualizar (opcional)
  accountId: number;        // ID del usuario que está actualizando la venta
}
