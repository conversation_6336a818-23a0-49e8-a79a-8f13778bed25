"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaCatalogRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const cia_company_1 = require("../../../domain/models/cia-company");
const condition_sale_1 = require("../../../domain/models/condition-sale");
const currency_1 = require("../../../domain/models/currency");
const document_type_1 = require("../../../domain/models/document-type");
const insurance_line_cia_sale_1 = require("../../../domain/models/insurance-line-cia-sale");
let PrismaCatalogRepository = class PrismaCatalogRepository {
    _prismaService;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
    }
    async getAllCiaCompanies() {
        const companies = await this._prismaService.ciaCompany.findMany();
        return companies.map(company => {
            const ciaCompany = new cia_company_1.CiaCompany();
            ciaCompany.cia_company_id = company.cia_company_id;
            ciaCompany.company_name = company.company_name;
            return ciaCompany;
        });
    }
    async getCiaCompanyById(id) {
        const company = await this._prismaService.ciaCompany.findUnique({
            where: { cia_company_id: id }
        });
        if (!company)
            return null;
        const ciaCompany = new cia_company_1.CiaCompany();
        ciaCompany.cia_company_id = company.cia_company_id;
        ciaCompany.company_name = company.company_name;
        return ciaCompany;
    }
    async getAllConditionSales() {
        const conditions = await this._prismaService.conditionSale.findMany();
        return conditions.map(condition => {
            const conditionSale = new condition_sale_1.ConditionSale();
            conditionSale.condition_sale_id = condition.condition_sale_id;
            conditionSale.condition_name = condition.condition_name;
            return conditionSale;
        });
    }
    async getConditionSaleById(id) {
        const condition = await this._prismaService.conditionSale.findUnique({
            where: { condition_sale_id: id }
        });
        if (!condition)
            return null;
        const conditionSale = new condition_sale_1.ConditionSale();
        conditionSale.condition_sale_id = condition.condition_sale_id;
        conditionSale.condition_name = condition.condition_name;
        return conditionSale;
    }
    async getAllCurrencies() {
        const currencies = await this._prismaService.currency.findMany();
        return currencies.map(currencyData => {
            const currency = new currency_1.Currency();
            currency.currency_id = currencyData.currency_id;
            currency.currency_type = currencyData.currency_type;
            return currency;
        });
    }
    async getCurrencyById(id) {
        const currencyData = await this._prismaService.currency.findUnique({
            where: { currency_id: id }
        });
        if (!currencyData)
            return null;
        const currency = new currency_1.Currency();
        currency.currency_id = currencyData.currency_id;
        currency.currency_type = currencyData.currency_type;
        return currency;
    }
    async getAllDocumentTypes() {
        const documentTypes = await this._prismaService.documentType.findMany();
        return documentTypes.map(docType => {
            const documentType = new document_type_1.DocumentType();
            documentType.document_type_id = docType.document_type_id;
            documentType.document_name = docType.document_name;
            return documentType;
        });
    }
    async getDocumentTypeById(id) {
        const docType = await this._prismaService.documentType.findUnique({
            where: { document_type_id: id }
        });
        if (!docType)
            return null;
        const documentType = new document_type_1.DocumentType();
        documentType.document_type_id = docType.document_type_id;
        documentType.document_name = docType.document_name;
        return documentType;
    }
    async getAllInsuranceLineCiaSales() {
        const insuranceLines = await this._prismaService.insuranceLineCiaSale.findMany();
        return insuranceLines.map(line => {
            const insuranceLine = new insurance_line_cia_sale_1.InsuranceLineCiaSale();
            insuranceLine.insurance_line_cia_sale_id = line.insurance_line_cia_sale_id;
            insuranceLine.insurance_name = line.insurance_name;
            return insuranceLine;
        });
    }
    async getInsuranceLineCiaSaleById(id) {
        const line = await this._prismaService.insuranceLineCiaSale.findUnique({
            where: { insurance_line_cia_sale_id: id }
        });
        if (!line)
            return null;
        const insuranceLine = new insurance_line_cia_sale_1.InsuranceLineCiaSale();
        insuranceLine.insurance_line_cia_sale_id = line.insurance_line_cia_sale_id;
        insuranceLine.insurance_name = line.insurance_name;
        return insuranceLine;
    }
};
exports.PrismaCatalogRepository = PrismaCatalogRepository;
exports.PrismaCatalogRepository = PrismaCatalogRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaCatalogRepository);
//# sourceMappingURL=prisma-catalog.repository.js.map