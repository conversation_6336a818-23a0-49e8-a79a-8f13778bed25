"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationBuilder = void 0;
const location_1 = require("../models/location");
class LocationBuilder {
    location;
    constructor() {
        this.reset();
    }
    reset() {
        this.location = new location_1.Location();
    }
    build() {
        const builtLocation = this.location;
        this.reset();
        return builtLocation;
    }
    withLocationId(locationId) {
        this.location.location_id = locationId;
        return this;
    }
    withAddress(address) {
        this.location.address = address;
        return this;
    }
    withDepartment(department) {
        this.location.department = department;
        return this;
    }
    withProvince(province) {
        this.location.province = province;
        return this;
    }
    withDistrict(district) {
        this.location.district = district;
        return this;
    }
}
exports.LocationBuilder = LocationBuilder;
//# sourceMappingURL=location.builder.js.map