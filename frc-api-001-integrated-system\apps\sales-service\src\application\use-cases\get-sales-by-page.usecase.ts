import { Inject } from "@nestjs/common";
import { SALE_REPOSITORY, ISaleRepository } from "../../domain/ports/repositories/sale.repository";
import { CATALOG_REPOSITORY, ICatalogRepository } from "../../domain/ports/repositories/catalog.repository";
import { CUSTOMER_REPOSITORY, ICustomerRepository } from "../../domain/ports/repositories/customer.repository";
import { SalePaginationDto } from "../dtos/sale-pagination.dto";
import { SaleListItemDto } from "../dtos/sale-list-item.dto";
import { Sale } from "../../domain/models/sale";

export const GET_SALES_BY_PAGE_USE_CASE = Symbol('GET_SALES_BY_PAGE_USE_CASE');
export class GetSalesByPageUseCase {
  constructor(
    @Inject(SALE_REPOSITORY)
    private readonly _saleRepository: ISaleRepository,
    @Inject(CATALOG_REPOSITORY)
    private readonly _catalogRepository: ICatalogRepository,
    @Inject(CUSTOMER_REPOSITORY)
    private readonly _customerRepository: ICustomerRepository
  ) {}

  async execute(page: number): Promise<SalePaginationDto> {
    const pageSize = 15; // Mostrar 15 ventas por página
    // Modificamos para adaptarnos a la interfaz del repositorio
    const sales = await this._saleRepository.findAll();
    
    // Implementamos la paginación manualmente ya que el repositorio no la soporta directamente
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSales = sales.slice(startIndex, endIndex);
    
    const totalSales = sales.length;
    const totalPages = Math.ceil(totalSales / pageSize);
    
    // Transformar las ventas al formato requerido para la lista
    const saleListItems = await this.transformSalesToListItems(paginatedSales);
    
    return {
      sales: paginatedSales, // Mantenemos los objetos originales por si se necesitan
      totalSales,
      page,
      totalPages
    };
  }

  private async transformSalesToListItems(sales: Sale[]): Promise<SaleListItemDto[]> {
    // Obtener todos los catálogos necesarios para transformar las ventas
    const [ciaCompanies, insuranceLines, documentTypes, currencies] = await Promise.all([
      this._catalogRepository.getAllCiaCompanies(),
      this._catalogRepository.getAllInsuranceLineCiaSales(),
      this._catalogRepository.getAllDocumentTypes(),
      this._catalogRepository.getAllCurrencies()
    ]);

    // Mapear los IDs a nombres para un acceso más rápido
    const ciaCompanyMap = new Map(ciaCompanies.map(c => [c.cia_company_id, c.company_name]));
    const insuranceLineMap = new Map(insuranceLines.map(il => [il.insurance_line_cia_sale_id, il.insurance_name]));
    const documentTypeMap = new Map(documentTypes.map(dt => [dt.document_type_id, dt.document_name]));
    const currencyMap = new Map(currencies.map(c => [c.currency_id, c.currency_type]));

    // Transformar cada venta
    return Promise.all(sales.map(async sale => {
      // Obtener el cliente asociado a la venta
      const customer = await this._customerRepository.findById(sale.customer_id);
      
      // Asegurarnos de que todos los campos requeridos tengan valores no undefined
      const saleItem: SaleListItemDto = {
        saleId: sale.sale_id ?? 0, // Usar 0 como valor por defecto si es undefined
        policyNumber: sale.policy_number ?? '',
        insurerName: ciaCompanyMap.get(sale.cia_company_id) || 'No especificado',
        insuranceLine: insuranceLineMap.get(sale.insurance_line_cia_sale_id) || 'No especificado',
        saleDate: sale.start_sale_date ?? new Date(),
        documentType: customer ? documentTypeMap.get(customer.document_type_id) || 'No especificado' : 'No especificado',
        documentNumber: customer ? customer.doi || 'No especificado' : 'No especificado',
        customerName: customer ? customer.full_name : 'No especificado',
        currency: currencyMap.get(sale.currency_id) || 'No especificado',
        netAmount: sale.net_amount ?? 0,
        totalAmount: sale.total_amount ?? 0
      };
      
      return saleItem;
    }));
  }
}
