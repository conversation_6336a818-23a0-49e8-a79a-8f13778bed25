import { IDeleteSaleTransaction } from "../../domain/ports/transactions/delete-sale.transaction";
import { IJwtService } from "../../domain/ports/services/jwt.service";
export declare const DELETE_SALE_USE_CASE: unique symbol;
export declare class DeleteSaleUseCase {
    private readonly _deleteSaleTransaction;
    private readonly _jwtService;
    constructor(_deleteSaleTransaction: IDeleteSaleTransaction, _jwtService: IJwtService);
    execute(saleId: number, hardDelete: boolean, token: string): Promise<void>;
}
