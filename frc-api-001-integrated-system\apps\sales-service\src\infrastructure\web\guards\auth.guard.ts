import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../../services/auth/auth.service';
import { Request } from 'express';

/**
 * Guardia de autenticación para proteger rutas que requieren autenticación
 * Extrae y valida el token JWT de la cabecera de autorización
 */
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly authService: AuthService) {}

  /**
   * Verifica si la solicitud puede activar la ruta protegida
   * @param context Contexto de ejecución
   * @returns true si la solicitud está autenticada, false en caso contrario
   * @throws UnauthorizedException si el token es inválido
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers.authorization;

    try {
      // Extraer y validar el token
      // Asegurarse de que authHeader no sea undefined antes de pasarlo al método
      const token = this.authService.extractTokenFromHeader(authHeader || '');
      const payload = this.authService.verifyToken(token);
      
      // Almacenar el payload decodificado en la solicitud para uso posterior
      request['user'] = payload;
      
      return true;
    } catch (error) {
      throw new UnauthorizedException('No autorizado: ' + error.message);
    }
  }
}
