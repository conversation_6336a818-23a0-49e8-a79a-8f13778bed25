"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSaleUseCase = exports.CREATE_SALE_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const create_sale_transaction_1 = require("../../domain/ports/transactions/create-sale.transaction");
const sale_builder_1 = require("../../domain/builders/sale.builder");
const customer_builder_1 = require("../../domain/builders/customer.builder");
const location_builder_1 = require("../../domain/builders/location.builder");
const jwt_service_1 = require("../../domain/ports/services/jwt.service");
exports.CREATE_SALE_USE_CASE = Symbol('CREATE_SALE_USE_CASE');
let CreateSaleUseCase = class CreateSaleUseCase {
    _createSaleTransaction;
    _jwtService;
    saleBuilder;
    customerBuilder;
    locationBuilder;
    constructor(_createSaleTransaction, _jwtService) {
        this._createSaleTransaction = _createSaleTransaction;
        this._jwtService = _jwtService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
        this.customerBuilder = new customer_builder_1.CustomerBuilder();
        this.locationBuilder = new location_builder_1.LocationBuilder();
    }
    async execute(saleData, token) {
        const userInfo = await this._jwtService.verify(token);
        const sale = this.saleBuilder
            .withPolicyNumber(saleData.policyNumber)
            .withStartSaleDate(new Date(saleData.startSaleDate))
            .withEndSaleDate(new Date(saleData.endSaleDate))
            .withNetAmount(saleData.netAmount)
            .withTotalAmount(saleData.totalAmount)
            .withAccountId(userInfo.accountId)
            .withConditionSaleId(saleData.conditionSaleId)
            .withCurrencyId(saleData.currencyId)
            .withCiaCompanyId(saleData.ciaCompanyId)
            .withInsuranceLineCiaSaleId(saleData.insuranceLineCiaSaleId)
            .build();
        const customer = this.customerBuilder
            .withFullName(saleData.customer.fullName)
            .withDoi(saleData.customer.doi)
            .withPhoneNumber(saleData.customer.phoneNumber)
            .withMobileNumber(saleData.customer.mobileNumber)
            .withDocumentTypeId(saleData.customer.documentTypeId)
            .build();
        const location = this.locationBuilder
            .withAddress(saleData.location.address)
            .withDepartment(saleData.location.department)
            .withProvince(saleData.location.province)
            .withDistrict(saleData.location.district)
            .build();
        const createSaleDto = {
            sale,
            customer,
            location,
            accountId: userInfo.accountId
        };
        return await this._createSaleTransaction.run(createSaleDto);
    }
};
exports.CreateSaleUseCase = CreateSaleUseCase;
exports.CreateSaleUseCase = CreateSaleUseCase = __decorate([
    __param(0, (0, common_1.Inject)(create_sale_transaction_1.CREATE_SALE_TRANSACTION)),
    __param(1, (0, common_1.Inject)(jwt_service_1.JWT_SERVICE)),
    __metadata("design:paramtypes", [Object, Object])
], CreateSaleUseCase);
//# sourceMappingURL=create-sale.usecase.js.map