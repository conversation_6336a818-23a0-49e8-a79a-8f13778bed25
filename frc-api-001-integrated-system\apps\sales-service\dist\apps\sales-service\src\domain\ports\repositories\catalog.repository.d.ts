import { CiaCompany } from '../../models/cia-company';
import { ConditionSale } from '../../models/condition-sale';
import { Currency } from '../../models/currency';
import { DocumentType } from '../../models/document-type';
import { InsuranceLineCiaSale } from '../../models/insurance-line-cia-sale';
export declare const CATALOG_REPOSITORY: unique symbol;
export interface ICatalogRepository {
    getAllCiaCompanies(): Promise<CiaCompany[]>;
    getCiaCompanyById(id: number): Promise<CiaCompany | null>;
    getAllConditionSales(): Promise<ConditionSale[]>;
    getConditionSaleById(id: number): Promise<ConditionSale | null>;
    getAllCurrencies(): Promise<Currency[]>;
    getCurrencyById(id: number): Promise<Currency | null>;
    getAllDocumentTypes(): Promise<DocumentType[]>;
    getDocumentTypeById(id: number): Promise<DocumentType | null>;
    getAllInsuranceLineCiaSales(): Promise<InsuranceLineCiaSale[]>;
    getInsuranceLineCiaSaleById(id: number): Promise<InsuranceLineCiaSale | null>;
}
