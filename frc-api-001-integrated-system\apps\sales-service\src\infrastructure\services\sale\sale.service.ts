import { Inject, Injectable } from '@nestjs/common';
import { Sale } from '../../../domain/models/sale';
import { CreateSaleDto } from '../../../domain/ports/transactions/dtos/create-sale.dto';
import { UpdateSaleDto } from '../../../domain/ports/transactions/dtos/update-sale.dto';
import { ISaleStoredProcedures, SALE_STORED_PROCEDURES } from '../../persistence/stored-procedures/sale-stored-procedures.interface';
import { SalesServicePrismaService } from '../../persistence/prisma.service';
import { SaleBuilder } from '../../../domain/builders/sale.builder';

/**
 * Servicio de infraestructura para operaciones de ventas
 * Actúa como puente entre los casos de uso y la capa de persistencia
 */
@Injectable()
export class SaleService {
  constructor(
    @Inject(SALE_STORED_PROCEDURES) private readonly saleStoredProcedures: ISaleStoredProcedures,
    private readonly prismaService: SalesServicePrismaService,
    private readonly saleBuilder: SaleBuilder
  ) {}

  /**
   * Crea una nueva venta con su cliente y ubicación asociados
   * @param createSaleDto DTO con los datos de la venta a crear
   * @returns La venta creada
   */
  async createSale(createSaleDto: CreateSaleDto): Promise<Sale> {
    // Extraer datos de venta, cliente y ubicación del DTO
    const { sale: saleData, customer, location, accountId } = createSaleDto;

    // Usar el procedimiento almacenado para crear la venta completa
    return this.saleStoredProcedures.createCompleteSale(
      {
        policy_number: saleData.policy_number,
        start_sale_date: saleData.start_sale_date,
        end_sale_date: saleData.end_sale_date,
        net_amount: saleData.net_amount,
        total_amount: saleData.total_amount,
        condition_sale_id: saleData.condition_sale_id,
        currency_id: saleData.currency_id,
        cia_company_id: saleData.cia_company_id,
        insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id,
        account_id: accountId
      },
      {
        full_name: customer.full_name,
        doi: customer.doi,
        phone_number: customer.phone_number,
        mobile_number: customer.mobile_number,
        document_type_id: customer.document_type_id
      },
      location ? {
        address: location.address,
        department: location.department,
        province: location.province,
        district: location.district
      } : undefined
    );
  }

  /**
   * Actualiza una venta existente
   * @param updateSaleDto DTO con los datos a actualizar
   * @returns La venta actualizada
   */
  async updateSale(updateSaleDto: UpdateSaleDto): Promise<Sale> {
    const { saleId, sale: saleData, customer, location, accountId } = updateSaleDto;

    // Usar el procedimiento almacenado para actualizar la venta completa
    return this.saleStoredProcedures.updateCompleteSale(
      saleId,
      {
        policy_number: saleData.policy_number,
        start_sale_date: saleData.start_sale_date,
        end_sale_date: saleData.end_sale_date,
        net_amount: saleData.net_amount,
        total_amount: saleData.total_amount,
        condition_sale_id: saleData.condition_sale_id,
        currency_id: saleData.currency_id,
        cia_company_id: saleData.cia_company_id,
        insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id,
        account_id: accountId
      },
      customer ? {
        full_name: customer.full_name,
        doi: customer.doi,
        phone_number: customer.phone_number,
        mobile_number: customer.mobile_number,
        document_type_id: customer.document_type_id
      } : undefined,
      location ? {
        address: location.address,
        department: location.department,
        province: location.province,
        district: location.district
      } : undefined
    );
  }

  /**
   * Renueva una venta existente
   * @param originalSaleId ID de la venta original
   * @param newSaleData Datos para la nueva venta
   * @param startDate Fecha de inicio de la nueva venta
   * @param endDate Fecha de fin de la nueva venta
   * @param keepCustomerInfo Indica si se debe mantener la información del cliente
   * @param accountId ID de la cuenta asociada
   * @returns La nueva venta creada como renovación
   */
  async renewSale(
    originalSaleId: number,
    newSaleData: {
      policy_number?: string;
      net_amount?: number;
      total_amount?: number;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
    },
    startDate: Date,
    endDate: Date,
    keepCustomerInfo: boolean,
    accountId: number
  ): Promise<Sale> {
    return this.saleStoredProcedures.renewSale(
      originalSaleId,
      {
        policy_number: newSaleData.policy_number,
        net_amount: newSaleData.net_amount,
        total_amount: newSaleData.total_amount,
        condition_sale_id: newSaleData.condition_sale_id,
        currency_id: newSaleData.currency_id,
        cia_company_id: newSaleData.cia_company_id,
        insurance_line_cia_sale_id: newSaleData.insurance_line_cia_sale_id
      },
      startDate,
      endDate,
      keepCustomerInfo,
      accountId
    );
  }

  /**
   * Obtiene una venta por su ID
   * @param id ID de la venta
   * @returns La venta encontrada o null
   */
  async getSaleById(id: number): Promise<Sale | null> {
    const sale = await this.prismaService.sale.findUnique({
      where: { sale_id: id },
      include: {
        Customer: {
          include: {
            Location: true
          }
        }
      }
    });

    if (!sale) {
      return null;
    }

    // Crear un objeto Sale directamente
    const domainSale = new Sale();
    domainSale.sale_id = sale.sale_id;
    domainSale.policy_number = sale.policy_number || undefined;
    domainSale.start_sale_date = sale.start_sale_date || undefined;
    domainSale.end_sale_date = sale.end_sale_date || undefined;
    domainSale.net_amount = sale.net_amount ? Number(sale.net_amount) : undefined;
    domainSale.total_amount = sale.total_amount ? Number(sale.total_amount) : undefined;
    domainSale.condition_sale_id = sale.condition_sale_id || undefined;
    domainSale.currency_id = sale.currency_id || undefined;
    domainSale.cia_company_id = sale.cia_company_id || undefined;
    domainSale.insurance_line_cia_sale_id = sale.insurance_line_cia_sale_id || undefined;
    domainSale.account_id = sale.account_id;
    domainSale.customer_id = sale.customer_id;
    
    return domainSale;
  }

  /**
   * Obtiene ventas con filtros avanzados y paginación
   * @param filters Filtros para la búsqueda
   * @param pagination Opciones de paginación
   * @returns Lista de ventas y total
   */
  async getSalesWithFilters(
    filters: {
      customerId?: number;
      customerDoi?: string;
      customerName?: string;
      policyNumber?: string;
      startDateFrom?: Date;
      startDateTo?: Date;
      endDateFrom?: Date;
      endDateTo?: Date;
      accountId?: number;
      conditionSaleId?: number;
      currencyId?: number;
      ciaCompanyId?: number;
      insuranceLineCiaSaleId?: number;
    },
    pagination?: {
      skip?: number;
      take?: number;
      orderBy?: string;
      orderDirection?: 'asc' | 'desc';
    }
  ): Promise<{ sales: Sale[]; total: number }> {
    return this.saleStoredProcedures.getSalesWithFilters(filters, pagination);
  }

  /**
   * Elimina una venta por su ID
   * @param id ID de la venta a eliminar
   * @returns true si se eliminó correctamente
   */
  async deleteSale(id: number): Promise<boolean> {
    try {
      await this.prismaService.sale.delete({
        where: { sale_id: id }
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}
