{"name": "frc-api-001-integrated-systems", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start:gateway": "nest start api-gateway", "dev:gateway": "nest start api-gateway --watch", "debug:gateway": "nest start api-gateway --debug --watch", "prod:gateway": "node dist/apps/api-gateway/main", "start:auth": "nest start auth-service", "dev:auth": "nest start auth-service --watch", "debug:auth": "nest start auth-service --debug --watch", "prod:auth": "node dist/apps/auth-service/main", "start:commissions": "nest start commissions-service", "dev:commissions": "nest start commissions-service --watch", "debug:commissions": "nest start commissions-service --debug --watch", "prod:commissions": "node dist/apps/commissions-service/main", "start:sales": "nest start sales-service", "dev:sales": "nest start sales-service --watch", "debug:sales": "nest start sales-service --debug --watch", "prod:sales": "node dist/apps/sales-service/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/api-gateway/test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/microservices": "^11.1.0", "@nestjs/platform-express": "^11.1.0", "@prisma/client": "^6.7.0", "bcrypt": "5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "16.5.0", "jsonwebtoken": "9.0.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "zod": "3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.21", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/"]}}