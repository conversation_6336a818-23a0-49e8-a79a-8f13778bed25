export declare class ApiResponsePresenter<T> {
    success: boolean;
    message: string;
    data?: T;
    timestamp: string;
    statusCode: number;
    constructor(success: boolean, message: string, data?: T, statusCode?: number);
    static success<T>(message: string, data?: T, statusCode?: number): ApiResponsePresenter<T>;
    static error<T>(message: string, data?: T, statusCode?: number): ApiResponsePresenter<T>;
}
