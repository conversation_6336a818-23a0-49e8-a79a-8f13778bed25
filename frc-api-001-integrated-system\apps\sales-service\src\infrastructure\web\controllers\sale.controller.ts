import { Body, Controller, Delete, Get, HttpStatus, Inject, Param, ParseIntPipe, Post, Put, Query, UseGuards, UnauthorizedException, Req } from '@nestjs/common';
import { Request } from 'express';
import { AuthGuard } from '../guards/auth.guard';
import { AuthService } from '../../services/auth/auth.service';
import { CREATE_SALE_USE_CASE, CreateSaleUseCase } from '../../../application/use-cases/create-sale.usecase';
import { UPDATE_SALE_USE_CASE, UpdateSaleUseCase } from '../../../application/use-cases/update-sale.usecase';
import { GET_SALE_BY_ID_USE_CASE, GetSaleByIdUseCase } from '../../../application/use-cases/get-sale-by-id.usecase';
import { GET_SALES_BY_PAGE_USE_CASE, GetSalesByPageUseCase } from '../../../application/use-cases/get-sales-by-page.usecase';
import { RENEW_SALE_USE_CASE, RenewSaleUseCase } from '../../../application/use-cases/renew-sale.usecase';
import { DELETE_SALE_USE_CASE, DeleteSaleUseCase } from '../../../application/use-cases/delete-sale.usecase';
import { CreateSaleRequestDto } from './dtos/create-sale.request.dto';
import { UpdateSaleRequestDto } from './dtos/update-sale.request.dto';
import { FilterSalesRequestDto } from './dtos/filter-sales.request.dto';
import { ApiResponsePresenter } from './presenters/api-response.presenter';
import { PaginatedResponsePresenter } from './presenters/paginated-response.presenter';
import { Sale } from '../../../domain/models/sale';
import { CreateSaleDto } from '../../../domain/ports/transactions/dtos/create-sale.dto';
import { UpdateSaleDto } from '../../../domain/ports/transactions/dtos/update-sale.dto';

/**
 * Controlador para gestionar operaciones de ventas
 */
@Controller('sales')
export class SaleController {
  constructor(
    @Inject(CREATE_SALE_USE_CASE)
    private readonly createSaleUseCase: CreateSaleUseCase,
    
    @Inject(UPDATE_SALE_USE_CASE)
    private readonly updateSaleUseCase: UpdateSaleUseCase,
    
    @Inject(GET_SALE_BY_ID_USE_CASE)
    private readonly getSaleByIdUseCase: GetSaleByIdUseCase,
    
    @Inject(GET_SALES_BY_PAGE_USE_CASE)
    private readonly getSalesByPageUseCase: GetSalesByPageUseCase,
    
    @Inject(RENEW_SALE_USE_CASE)
    private readonly renewSaleUseCase: RenewSaleUseCase,
    
    @Inject(DELETE_SALE_USE_CASE)
    private readonly deleteSaleUseCase: DeleteSaleUseCase,
    
    private readonly authService: AuthService
  ) {}

  /**
   * Crea una nueva venta
   */
  @Post()
  @UseGuards(AuthGuard)
  async createSale(
    @Body() createSaleRequestDto: CreateSaleRequestDto,
    @Req() request: Request
  ): Promise<ApiResponsePresenter<Sale>> {
    try {
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      // Los casos de uso esperan recibir directamente los datos y el token
      // No es necesario crear un DTO de dominio aquí, ya que el caso de uso se encarga de eso
      // Mapear los datos del DTO de solicitud al formato que espera el caso de uso
      const saleData = {
        policyNumber: createSaleRequestDto.sale.policy_number,
        startSaleDate: createSaleRequestDto.sale.start_sale_date,
        endSaleDate: createSaleRequestDto.sale.end_sale_date,
        netAmount: createSaleRequestDto.sale.net_amount,
        totalAmount: createSaleRequestDto.sale.total_amount,
        conditionSaleId: createSaleRequestDto.sale.condition_sale_id,
        currencyId: createSaleRequestDto.sale.currency_id,
        ciaCompanyId: createSaleRequestDto.sale.cia_company_id,
        insuranceLineCiaSaleId: createSaleRequestDto.sale.insurance_line_cia_sale_id,
        customer: {
          fullName: createSaleRequestDto.customer.full_name,
          doi: createSaleRequestDto.customer.doi,
          phoneNumber: createSaleRequestDto.customer.phone_number,
          mobileNumber: createSaleRequestDto.customer.mobile_number,
          documentTypeId: createSaleRequestDto.customer.document_type_id
        },
        location: createSaleRequestDto.location ? {
          address: createSaleRequestDto.location.address,
          department: createSaleRequestDto.location.department,
          province: createSaleRequestDto.location.province,
          district: createSaleRequestDto.location.district
        } : undefined
      };
      
      const createdSale = await this.createSaleUseCase.execute(saleData, token);
    
      return ApiResponsePresenter.success<Sale>(
        HttpStatus.CREATED.toString(),
        createdSale,
        HttpStatus.CREATED
      );
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        return ApiResponsePresenter.error<Sale>(
          HttpStatus.UNAUTHORIZED.toString(),
          undefined,
          HttpStatus.UNAUTHORIZED
        );
      }
      return ApiResponsePresenter.error<Sale>(
        HttpStatus.INTERNAL_SERVER_ERROR.toString(),
        undefined,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Actualiza una venta existente
   */
  @Put(':id')
  @UseGuards(AuthGuard)
  async updateSale(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSaleRequestDto: UpdateSaleRequestDto,
    @Req() request: Request
  ): Promise<ApiResponsePresenter<Sale>> {
    try {
      // Verificar que el ID de la ruta coincida con el ID en el cuerpo
      if (id !== updateSaleRequestDto.saleId) {
        return ApiResponsePresenter.error<Sale>(
          HttpStatus.BAD_REQUEST.toString(),
          undefined,
          HttpStatus.BAD_REQUEST
        );
      }
      
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      // Mapear los datos del DTO de solicitud al formato que espera el caso de uso
      const saleData = {
        policyNumber: updateSaleRequestDto.sale.policy_number,
        startSaleDate: updateSaleRequestDto.sale.start_sale_date,
        endSaleDate: updateSaleRequestDto.sale.end_sale_date,
        netAmount: updateSaleRequestDto.sale.net_amount,
        totalAmount: updateSaleRequestDto.sale.total_amount,
        conditionSaleId: updateSaleRequestDto.sale.condition_sale_id,
        currencyId: updateSaleRequestDto.sale.currency_id,
        ciaCompanyId: updateSaleRequestDto.sale.cia_company_id,
        insuranceLineCiaSaleId: updateSaleRequestDto.sale.insurance_line_cia_sale_id,
        customer: {
          // No incluimos customerId si no existe en el DTO
          fullName: updateSaleRequestDto.customer.full_name,
          doi: updateSaleRequestDto.customer.doi,
          phoneNumber: updateSaleRequestDto.customer.phone_number,
          mobileNumber: updateSaleRequestDto.customer.mobile_number,
          documentTypeId: updateSaleRequestDto.customer.document_type_id
        },
        location: updateSaleRequestDto.location ? {
          // No incluimos locationId si no existe en el DTO
          address: updateSaleRequestDto.location.address,
          department: updateSaleRequestDto.location.department,
          province: updateSaleRequestDto.location.province,
          district: updateSaleRequestDto.location.district
        } : undefined
      };
      
      const updatedSale = await this.updateSaleUseCase.execute(id, saleData, token);
    
      return ApiResponsePresenter.success<Sale>(
        HttpStatus.OK.toString(),
        updatedSale
      );
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        return ApiResponsePresenter.error<Sale>(
          HttpStatus.UNAUTHORIZED.toString(),
          undefined,
          HttpStatus.UNAUTHORIZED
        );
      }
      return ApiResponsePresenter.error<Sale>(
        HttpStatus.INTERNAL_SERVER_ERROR.toString(),
        undefined,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Renueva una venta existente
   */
  @Post(':id/renew')
  @UseGuards(AuthGuard)
  async renewSale(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Request
  ): Promise<ApiResponsePresenter<Sale>> {
    try {
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      // El caso de uso espera 3 argumentos: originalSaleId, renewalData, token
      // Como no tenemos datos de renovación específicos, pasamos un objeto vacío
      const renewedSale = await this.renewSaleUseCase.execute(id, {}, token);
    
      return ApiResponsePresenter.success<Sale>(
        HttpStatus.OK.toString(),
        renewedSale
      );
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        return ApiResponsePresenter.error<Sale>(
          HttpStatus.UNAUTHORIZED.toString(),
          undefined,
          HttpStatus.UNAUTHORIZED
        );
      }
      return ApiResponsePresenter.error<Sale>(
        HttpStatus.INTERNAL_SERVER_ERROR.toString(),
        undefined,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Obtiene una venta por su ID
   */
  @Get(':id')
  @UseGuards(AuthGuard)
  async getSaleById(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Request
  ): Promise<ApiResponsePresenter<Sale>> {
    try {
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      const saleData = await this.getSaleByIdUseCase.execute(id);
      
      if (!saleData) {
        return ApiResponsePresenter.error<Sale>(
          HttpStatus.NOT_FOUND.toString(),
          undefined,
          HttpStatus.NOT_FOUND
        );
      }
    
      // El caso de uso devuelve un objeto con sale, customer y location
      // Pero ApiResponsePresenter espera un objeto de tipo Sale
      // Devolvemos solo el objeto sale que es de tipo Sale
      return ApiResponsePresenter.success<Sale>(
        HttpStatus.OK.toString(),
        saleData.sale
      );
    } catch (error) {
      return ApiResponsePresenter.error<Sale>(
        HttpStatus.INTERNAL_SERVER_ERROR.toString(),
        undefined,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Obtiene ventas con filtros y paginación
   */
  @Get()
  @UseGuards(AuthGuard)
  async getSalesWithFilters(
    @Query() filterDto: FilterSalesRequestDto,
    @Req() request: Request
  ): Promise<PaginatedResponsePresenter<Sale>> {
    try {
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      const { page = 1, limit = 10, ...filters } = filterDto;
      
      // El caso de uso solo espera el número de página como argumento
      const result = await this.getSalesByPageUseCase.execute(page);
    
      // Adaptar la respuesta al formato que espera el PaginatedResponsePresenter
      return PaginatedResponsePresenter.fromPagination<Sale>(
        result.sales,
        result.totalSales,
        page,
        limit,
        HttpStatus.OK.toString()
      );
    } catch (error) {
      // Como no podemos devolver un PaginatedResponsePresenter con error, lanzamos una excepción
      // que será capturada por el filtro global de excepciones
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR.toString());
    }
  }

  /**
   * Elimina una venta
   */
  @Delete(':id')
  @UseGuards(AuthGuard)
  async deleteSale(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Request
  ): Promise<ApiResponsePresenter<void>> {
    try {
      // Extraer y validar el token de la solicitud usando AuthService
      const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
      // Verificar que el token sea válido
      this.authService.verifyToken(token);
      
      // El caso de uso espera 3 argumentos: saleId, hardDelete, token
      // Por defecto, usamos false para hardDelete (borrado lógico)
      await this.deleteSaleUseCase.execute(id, false, token);
      
      return ApiResponsePresenter.success<void>(
        HttpStatus.NO_CONTENT.toString(),
        undefined,
        HttpStatus.NO_CONTENT
      );
    } catch (error) {
      return ApiResponsePresenter.error<void>(
        HttpStatus.INTERNAL_SERVER_ERROR.toString(),
        undefined,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
