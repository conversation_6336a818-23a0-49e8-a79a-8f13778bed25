"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteSaleUseCase = exports.DELETE_SALE_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const delete_sale_transaction_1 = require("../../domain/ports/transactions/delete-sale.transaction");
const jwt_service_1 = require("../../domain/ports/services/jwt.service");
exports.DELETE_SALE_USE_CASE = Symbol('DELETE_SALE_USE_CASE');
let DeleteSaleUseCase = class DeleteSaleUseCase {
    _deleteSaleTransaction;
    _jwtService;
    constructor(_deleteSaleTransaction, _jwtService) {
        this._deleteSaleTransaction = _deleteSaleTransaction;
        this._jwtService = _jwtService;
    }
    async execute(saleId, hardDelete, token) {
        const userInfo = await this._jwtService.verify(token);
        const deleteSaleDto = {
            saleId,
            accountId: userInfo.accountId,
            hardDelete
        };
        await this._deleteSaleTransaction.run(deleteSaleDto);
    }
};
exports.DeleteSaleUseCase = DeleteSaleUseCase;
exports.DeleteSaleUseCase = DeleteSaleUseCase = __decorate([
    __param(0, (0, common_1.Inject)(delete_sale_transaction_1.DELETE_SALE_TRANSACTION)),
    __param(1, (0, common_1.Inject)(jwt_service_1.JWT_SERVICE)),
    __metadata("design:paramtypes", [Object, Object])
], DeleteSaleUseCase);
//# sourceMappingURL=delete-sale.usecase.js.map