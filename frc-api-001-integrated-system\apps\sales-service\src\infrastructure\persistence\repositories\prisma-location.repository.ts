import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { LOCATION_REPOSITORY, ILocationRepository } from "../../../domain/ports/repositories/location.repository";
import { Location } from "../../../domain/models/location";
import { LocationBuilder } from "../../../domain/builders/location.builder";

@Injectable()
export class PrismaLocationRepository implements ILocationRepository {
  private readonly locationBuilder: LocationBuilder;

  constructor(
    private readonly _prismaService: PrismaService,
  ) {
    this.locationBuilder = new LocationBuilder();
  }

  async findAll(): Promise<Location[]> {
    const locations = await this._prismaService.location.findMany();
    return locations.map(location => this.mapPrismaToLocation(location));
  }

  async findById(id: number): Promise<Location | null> {
    const location = await this._prismaService.location.findUnique({
      where: { location_id: id }
    });

    if (!location) return null;

    return this.mapPrismaToLocation(location);
  }

  async create(location: Location): Promise<Location> {
    const createdLocation = await this._prismaService.location.create({
      data: {
        address: location.address,
        department: location.department,
        province: location.province,
        district: location.district
      }
    });

    return this.mapPrismaToLocation(createdLocation);
  }

  async update(id: number, location: Location): Promise<Location> {
    const updatedLocation = await this._prismaService.location.update({
      where: { location_id: id },
      data: {
        address: location.address,
        department: location.department,
        province: location.province,
        district: location.district
      }
    });

    return this.mapPrismaToLocation(updatedLocation);
  }

  async delete(id: number): Promise<void> {
    await this._prismaService.location.delete({
      where: { location_id: id }
    });
  }

  // Métodos específicos para obtener listas de ubicaciones
  async getAllDepartments(): Promise<string[]> {
    const result = await this._prismaService.location.findMany({
      distinct: ['department'],
      select: { department: true }
    });
    
    return result
      .map(item => item.department)
      .filter(department => department !== null && department !== undefined) as string[];
  }

  async getProvincesByDepartment(department: string): Promise<string[]> {
    const result = await this._prismaService.location.findMany({
      where: { department },
      distinct: ['province'],
      select: { province: true }
    });
    
    return result
      .map(item => item.province)
      .filter(province => province !== null && province !== undefined) as string[];
  }

  async getDistrictsByProvince(province: string): Promise<string[]> {
    const result = await this._prismaService.location.findMany({
      where: { province },
      distinct: ['district'],
      select: { district: true }
    });
    
    return result
      .map(item => item.district)
      .filter(district => district !== null && district !== undefined) as string[];
  }

  // Método auxiliar para mapear entidades de Prisma a modelos de dominio
  private mapPrismaToLocation(prismaEntity: any): Location {
    return this.locationBuilder
      .withLocationId(prismaEntity.location_id)
      .withAddress(prismaEntity.address)
      .withDepartment(prismaEntity.department)
      .withProvince(prismaEntity.province)
      .withDistrict(prismaEntity.district)
      .build();
  }
}
