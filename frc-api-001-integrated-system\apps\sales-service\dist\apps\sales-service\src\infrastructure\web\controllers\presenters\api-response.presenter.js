"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiResponsePresenter = void 0;
const common_1 = require("@nestjs/common");
class ApiResponsePresenter {
    success;
    message;
    data;
    timestamp;
    statusCode;
    constructor(success, message, data, statusCode = common_1.HttpStatus.OK) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.timestamp = new Date().toISOString();
        this.statusCode = statusCode;
    }
    static success(message, data, statusCode = common_1.HttpStatus.OK) {
        return new ApiResponsePresenter(true, message, data, statusCode);
    }
    static error(message, data, statusCode = common_1.HttpStatus.BAD_REQUEST) {
        return new ApiResponsePresenter(false, message, data, statusCode);
    }
}
exports.ApiResponsePresenter = ApiResponsePresenter;
//# sourceMappingURL=api-response.presenter.js.map