import { ISaleRepository } from "../../domain/ports/repositories/sale.repository";
import { ICatalogRepository } from "../../domain/ports/repositories/catalog.repository";
import { ICustomerRepository } from "../../domain/ports/repositories/customer.repository";
import { SalePaginationDto } from "../dtos/sale-pagination.dto";
export declare const GET_SALES_BY_PAGE_USE_CASE: unique symbol;
export declare class GetSalesByPageUseCase {
    private readonly _saleRepository;
    private readonly _catalogRepository;
    private readonly _customerRepository;
    constructor(_saleRepository: ISaleRepository, _catalogRepository: ICatalogRepository, _customerRepository: ICustomerRepository);
    execute(page: number): Promise<SalePaginationDto>;
    private transformSalesToListItems;
}
