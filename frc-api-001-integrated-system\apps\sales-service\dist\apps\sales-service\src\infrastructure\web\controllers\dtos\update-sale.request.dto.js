"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSaleRequestDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const create_sale_request_dto_1 = require("./create-sale.request.dto");
class UpdateSaleRequestDto {
    saleId;
    sale;
    customer;
    location;
}
exports.UpdateSaleRequestDto = UpdateSaleRequestDto;
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateSaleRequestDto.prototype, "saleId", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_sale_request_dto_1.CreateSaleInfoDto),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", create_sale_request_dto_1.CreateSaleInfoDto)
], UpdateSaleRequestDto.prototype, "sale", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_sale_request_dto_1.CreateCustomerDto),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", create_sale_request_dto_1.CreateCustomerDto)
], UpdateSaleRequestDto.prototype, "customer", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_sale_request_dto_1.CreateLocationDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", create_sale_request_dto_1.CreateLocationDto)
], UpdateSaleRequestDto.prototype, "location", void 0);
//# sourceMappingURL=update-sale.request.dto.js.map