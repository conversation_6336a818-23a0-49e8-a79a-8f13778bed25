import { Sale } from '../../../domain/models/sale';
import { Decimal } from '@prisma/client/runtime/library';

/**
 * Interfaz para los procedimientos almacenados relacionados con ventas
 */
export interface ISaleStoredProcedures {
  /**
   * Procedimiento para crear una venta completa con cliente y ubicación
   */
  createCompleteSale(
    saleData: {
      policy_number?: string;
      start_sale_date?: Date;
      end_sale_date?: Date;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
      account_id: number;
    },
    customerData: {
      full_name: string;
      doi?: string;
      phone_number?: string;
      mobile_number?: string;
      document_type_id: number;
    },
    locationData?: {
      address?: string;
      department?: string;
      province?: string;
      district?: string;
    }
  ): Promise<Sale>;

  /**
   * Procedimiento para actualizar una venta completa con cliente y ubicación
   */
  updateCompleteSale(
    saleId: number,
    saleData: {
      policy_number?: string;
      start_sale_date?: Date;
      end_sale_date?: Date;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
      account_id?: number;
    },
    customerData?: {
      full_name?: string;
      doi?: string;
      phone_number?: string;
      mobile_number?: string;
      document_type_id?: number;
    },
    locationData?: {
      address?: string;
      department?: string;
      province?: string;
      district?: string;
    }
  ): Promise<Sale>;

  /**
   * Procedimiento para renovar una venta existente
   */
  renewSale(
    originalSaleId: number,
    newSaleData: {
      policy_number?: string;
      net_amount?: number | Decimal;
      total_amount?: number | Decimal;
      condition_sale_id?: number;
      currency_id?: number;
      cia_company_id?: number;
      insurance_line_cia_sale_id?: number;
    },
    startDate: Date,
    endDate: Date,
    keepCustomerInfo: boolean,
    accountId: number
  ): Promise<Sale>;

  /**
   * Procedimiento para obtener ventas con filtros avanzados
   */
  getSalesWithFilters(
    filters: {
      customerId?: number;
      customerDoi?: string;
      customerName?: string;
      policyNumber?: string;
      startDateFrom?: Date;
      startDateTo?: Date;
      endDateFrom?: Date;
      endDateTo?: Date;
      accountId?: number;
      conditionSaleId?: number;
      currencyId?: number;
      ciaCompanyId?: number;
      insuranceLineCiaSaleId?: number;
    },
    pagination?: {
      skip?: number;
      take?: number;
      orderBy?: string;
      orderDirection?: 'asc' | 'desc';
    }
  ): Promise<{ sales: Sale[]; total: number }>;
}

// Token para inyección de dependencias
export const SALE_STORED_PROCEDURES = 'SALE_STORED_PROCEDURES';
