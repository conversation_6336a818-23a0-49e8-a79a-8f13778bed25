import { Injectable } from "@nestjs/common";
import { SalesServicePrismaService } from "../prisma.service";
import { IUpdateSaleTransaction, UPDATE_SALE_TRANSACTION } from "../../../domain/ports/transactions/update-sale.transaction";
import { UpdateSaleDto } from "../../../domain/ports/transactions/dtos/update-sale.dto";
import { Sale } from "../../../domain/models/sale";
import { SaleBuilder } from "../../../domain/builders/sale.builder";

@Injectable()
export class PrismaUpdateSaleTransaction implements IUpdateSaleTransaction {
  private readonly saleBuilder: SaleBuilder;

  constructor(
    private readonly _prismaService: SalesServicePrismaService,
  ) {
    this.saleBuilder = new SaleBuilder();
  }

  async run(updateSaleDto: UpdateSaleDto): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this._prismaService.$transaction(async (prisma) => {
      // 1. Verificar que la venta existe
      const existingSale = await prisma.sale.findUnique({
        where: { sale_id: updateSaleDto.saleId },
        include: { Customer: true }
      });

      if (!existingSale) {
        throw new Error(`Venta con ID ${updateSaleDto.saleId} no encontrada`);
      }

      // 2. Actualizar la ubicación si se proporcionó
      if (updateSaleDto.location && existingSale.Customer.location_id) {
        await prisma.location.update({
          where: { location_id: existingSale.Customer.location_id },
          data: {
            address: updateSaleDto.location.address,
            department: updateSaleDto.location.department,
            province: updateSaleDto.location.province,
            district: updateSaleDto.location.district
          }
        });
      } else if (updateSaleDto.location) {
        // Crear nueva ubicación si no existe
        const location = await prisma.location.create({
          data: {
            address: updateSaleDto.location.address,
            department: updateSaleDto.location.department,
            province: updateSaleDto.location.province,
            district: updateSaleDto.location.district
          }
        });

        // Actualizar el cliente con la nueva ubicación
        if (updateSaleDto.customer) {
          updateSaleDto.customer = {
            ...updateSaleDto.customer,
            location_id: location.location_id
          };
        } else {
          await prisma.customer.update({
            where: { customer_id: existingSale.customer_id },
            data: { location_id: location.location_id }
          });
        }
      }

      // 3. Actualizar el cliente si se proporcionó
      if (updateSaleDto.customer) {
        await prisma.customer.update({
          where: { customer_id: existingSale.customer_id },
          data: {
            full_name: updateSaleDto.customer.full_name,
            doi: updateSaleDto.customer.doi,
            phone_number: updateSaleDto.customer.phone_number,
            mobile_number: updateSaleDto.customer.mobile_number,
            document_type_id: updateSaleDto.customer.document_type_id,
            location_id: (updateSaleDto.customer as any).location_id
          }
        });
      }

      // 3. Actualizar la venta
      const updatedSale = await prisma.sale.update({
        where: { sale_id: updateSaleDto.saleId },
        data: {
          policy_number: updateSaleDto.sale.policy_number || undefined,
          start_sale_date: updateSaleDto.sale.start_sale_date || undefined,
          end_sale_date: updateSaleDto.sale.end_sale_date || undefined,
          net_amount: updateSaleDto.sale.net_amount as any,
          total_amount: updateSaleDto.sale.total_amount as any,
          condition_sale_id: updateSaleDto.sale.condition_sale_id || undefined,
          currency_id: updateSaleDto.sale.currency_id || undefined,
          cia_company_id: updateSaleDto.sale.cia_company_id || undefined,
          insurance_line_cia_sale_id: updateSaleDto.sale.insurance_line_cia_sale_id || undefined,
          customer_id: existingSale.customer_id,
          account_id: updateSaleDto.accountId || existingSale.account_id
        }
      });

      // 4. Mapear la entidad Prisma a un modelo de dominio
      return this.saleBuilder
        .withSaleId(updatedSale.sale_id)
        .withPolicyNumber(updatedSale.policy_number || undefined)
        .withStartSaleDate(updatedSale.start_sale_date || undefined)
        .withEndSaleDate(updatedSale.end_sale_date || undefined)
        .withNetAmount(Number(updatedSale.net_amount) || 0)
        .withTotalAmount(Number(updatedSale.total_amount) || 0)
        .withConditionSaleId(updatedSale.condition_sale_id || undefined)
        .withCurrencyId(updatedSale.currency_id || undefined)
        .withCiaCompanyId(updatedSale.cia_company_id || undefined)
        .withInsuranceLineCiaSaleId(updatedSale.insurance_line_cia_sale_id || undefined)
        .withCustomerId(updatedSale.customer_id)
        .withAccountId(updatedSale.account_id)
        .build();
    });
  }
}
