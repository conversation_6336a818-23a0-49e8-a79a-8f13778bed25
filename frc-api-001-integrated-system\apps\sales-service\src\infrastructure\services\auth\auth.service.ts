import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '../../config';
import * as jwt from 'jsonwebtoken';

/**
 * Servicio para manejar la autenticación y validación de tokens
 */
@Injectable()
export class AuthService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * Extrae y valida el token JWT de la cabecera de autorización
   * @param authHeader Cabecera de autorización
   * @returns Token JWT validado
   * @throws UnauthorizedException si el token es inválido o no existe
   */
  extractTokenFromHeader(authHeader: string): string {
    if (!authHeader) {
      throw new UnauthorizedException('No se proporcionó token de autorización');
    }

    const [type, token] = authHeader.split(' ');

    if (type !== 'Bearer' || !token) {
      throw new UnauthorizedException('Formato de token inválido');
    }

    return token;
  }

  /**
   * Verifica y decodifica un token JWT
   * @param token Token JWT a verificar
   * @returns Payload decodificado del token
   * @throws UnauthorizedException si el token es inválido
   */
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.configService.jwtAccessSecret);
    } catch (error) {
      throw new UnauthorizedException('Token inválido o expirado');
    }
  }

  /**
   * Extrae el ID de cuenta del payload del token
   * @param payload Payload del token JWT
   * @returns ID de la cuenta
   * @throws UnauthorizedException si no se encuentra el ID de cuenta
   */
  extractAccountId(payload: any): number {
    if (!payload || !payload.accountId) {
      throw new UnauthorizedException('Token no contiene ID de cuenta');
    }
    return payload.accountId;
  }
}
