"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.envs = void 0;
const zod_1 = require("zod");
const dotenv_1 = require("dotenv");
const path = require("path");
(0, dotenv_1.config)({ path: path.join(process.cwd(), '.env') });
const envSchema = zod_1.z.object({
    SALES_MICROSERVICE_HOST: zod_1.z.string().nonempty(),
    SALES_MICROSERVICE_PORT: zod_1.z.coerce.number(),
    JWT_ACCESS_SECRET: zod_1.z.string().nonempty(),
    SALES_SECRET_WORD: zod_1.z.string().nonempty(),
});
console.log('Environment variables check:');
console.log('SALES_MICROSERVICE_HOST:', process.env.SALES_MICROSERVICE_HOST);
console.log('SALES_MICROSERVICE_PORT:', process.env.SALES_MICROSERVICE_PORT);
console.log('JWT_ACCESS_SECRET:', process.env.JWT_ACCESS_SECRET);
console.log('SALES_SECRET_WORD:', process.env.SALES_SECRET_WORD);
const { data, success } = envSchema.safeParse(process.env);
if (!success) {
    console.error('Validation errors:', envSchema.safeParse(process.env).error);
    throw new Error('Invalid environment variables for sales service');
}
exports.envs = {
    salesServiceHost: data.SALES_MICROSERVICE_HOST,
    salesServicePort: data.SALES_MICROSERVICE_PORT,
    jwtAccessSecret: data.JWT_ACCESS_SECRET,
    secretWord: data.SALES_SECRET_WORD,
};
//# sourceMappingURL=envs.js.map