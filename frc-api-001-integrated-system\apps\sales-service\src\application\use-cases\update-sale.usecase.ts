import { Inject } from "@nestjs/common";
import { UPDATE_SALE_TRANSACTION, IUpdateSaleTransaction } from "../../domain/ports/transactions/update-sale.transaction";
import { UpdateSaleDto } from "../../domain/ports/transactions/dtos/update-sale.dto";
import { Sale } from "../../domain/models/sale";
import { Customer } from "../../domain/models/customer";
import { Location } from "../../domain/models/location";
import { SaleBuilder } from "../../domain/builders/sale.builder";
import { CustomerBuilder } from "../../domain/builders/customer.builder";
import { LocationBuilder } from "../../domain/builders/location.builder";
import { JWT_SERVICE, IJwtService } from "../../domain/ports/services/jwt.service";

export const UPDATE_SALE_USE_CASE = Symbol('UPDATE_SALE_USE_CASE');
export class UpdateSaleUseCase {
  private readonly saleBuilder: SaleBuilder;
  private readonly customerBuilder: CustomerBuilder;
  private readonly locationBuilder: LocationBuilder;

  constructor(
    @Inject(UPDATE_SALE_TRANSACTION)
    private readonly _updateSaleTransaction: IUpdateSaleTransaction,
    @Inject(JWT_SERVICE)
    private readonly _jwtService: IJwtService
  ) {
    this.saleBuilder = new SaleBuilder();
    this.customerBuilder = new CustomerBuilder();
    this.locationBuilder = new LocationBuilder();
  }

  async execute(saleId: number, saleData: any, token: string): Promise<Sale> {
    // Extraer la información del usuario del token JWT
    const userInfo = await this._jwtService.verify(token);
    
    // Construir el objeto Sale con los datos actualizados como un Partial<Sale>
    // Ya que UpdateSaleDto espera un Partial<Sale>
    const sale: Partial<Sale> = {
      sale_id: saleId,
      policy_number: saleData.policyNumber,
      start_sale_date: new Date(saleData.startSaleDate),
      end_sale_date: new Date(saleData.endSaleDate),
      net_amount: saleData.netAmount,
      total_amount: saleData.totalAmount,
      condition_sale_id: saleData.conditionSaleId,
      currency_id: saleData.currencyId,
      cia_company_id: saleData.ciaCompanyId,
      insurance_line_cia_sale_id: saleData.insuranceLineCiaSaleId,
      account_id: userInfo.accountId
    };
    
    // Construir el objeto Customer como un Partial<Customer> si existe
    let customer: Partial<Customer> | undefined = undefined;
    if (saleData.customer) {
      customer = {
        customer_id: saleData.customer.customerId,
        full_name: saleData.customer.fullName,
        doi: saleData.customer.doi,
        phone_number: saleData.customer.phoneNumber,
        mobile_number: saleData.customer.mobileNumber,
        document_type_id: saleData.customer.documentTypeId
      };
    }
    
    // Construir el objeto Location como un Partial<Location> si existe
    let location: Partial<Location> | undefined = undefined;
    if (saleData.location) {
      location = {
        // Usar los nombres de propiedades que coinciden con el modelo Location
        location_id: saleData.location.locationId,
        address: saleData.location.address,
        department: saleData.location.department,
        province: saleData.location.province,
        district: saleData.location.district
      };
    }
    
    // Crear el DTO para la transacción
    const updateSaleDto: UpdateSaleDto = {
      saleId,
      sale,
      customer,
      location,
      accountId: userInfo.accountId
    };
    
    // Ejecutar la transacción
    return await this._updateSaleTransaction.run(updateSaleDto);
  }
}
