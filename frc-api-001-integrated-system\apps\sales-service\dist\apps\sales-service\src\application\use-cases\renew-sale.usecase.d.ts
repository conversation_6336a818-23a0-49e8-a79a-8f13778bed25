import { IRenewSaleTransaction } from "../../domain/ports/transactions/renew-sale.transaction";
import { Sale } from "../../domain/models/sale";
import { IJwtService } from "../../domain/ports/services/jwt.service";
export declare const RENEW_SALE_USE_CASE: unique symbol;
export declare class RenewSaleUseCase {
    private readonly _renewSaleTransaction;
    private readonly _jwtService;
    constructor(_renewSaleTransaction: IRenewSaleTransaction, _jwtService: IJwtService);
    execute(originalSaleId: number, renewalData: any, token: string): Promise<Sale>;
}
