"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaSaleRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../../../../libs/prisma/prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let PrismaSaleRepository = class PrismaSaleRepository {
    _prismaService;
    saleBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
    }
    async findAll() {
        const sales = await this._prismaService.sale.findMany();
        return sales.map(sale => this.mapPrismaToSale(sale));
    }
    async findById(id) {
        const sale = await this._prismaService.sale.findUnique({
            where: { sale_id: id }
        });
        if (!sale)
            return null;
        return this.mapPrismaToSale(sale);
    }
    async create(sale) {
        const createdSale = await this._prismaService.sale.create({
            data: {
                policy_number: sale.policy_number,
                start_sale_date: sale.start_sale_date,
                end_sale_date: sale.end_sale_date,
                net_amount: sale.net_amount,
                total_amount: sale.total_amount,
                condition_sale_id: sale.condition_sale_id,
                currency_id: sale.currency_id,
                cia_company_id: sale.cia_company_id,
                insurance_line_cia_sale_id: sale.insurance_line_cia_sale_id,
                customer_id: sale.customer_id,
                account_id: sale.account_id
            }
        });
        return this.mapPrismaToSale(createdSale);
    }
    async update(id, sale) {
        const updatedSale = await this._prismaService.sale.update({
            where: { sale_id: id },
            data: {
                policy_number: sale.policy_number,
                start_sale_date: sale.start_sale_date,
                end_sale_date: sale.end_sale_date,
                net_amount: sale.net_amount,
                total_amount: sale.total_amount,
                condition_sale_id: sale.condition_sale_id,
                currency_id: sale.currency_id,
                cia_company_id: sale.cia_company_id,
                insurance_line_cia_sale_id: sale.insurance_line_cia_sale_id,
                customer_id: sale.customer_id,
                account_id: sale.account_id
            }
        });
        return this.mapPrismaToSale(updatedSale);
    }
    async delete(id) {
        await this._prismaService.sale.delete({
            where: { sale_id: id }
        });
    }
    mapPrismaToSale(prismaEntity) {
        return this.saleBuilder
            .withSaleId(prismaEntity.sale_id)
            .withPolicyNumber(prismaEntity.policy_number)
            .withStartSaleDate(prismaEntity.start_sale_date)
            .withEndSaleDate(prismaEntity.end_sale_date)
            .withNetAmount(prismaEntity.net_amount)
            .withTotalAmount(prismaEntity.total_amount)
            .withConditionSaleId(prismaEntity.condition_sale_id)
            .withCurrencyId(prismaEntity.currency_id)
            .withCiaCompanyId(prismaEntity.cia_company_id)
            .withInsuranceLineCiaSaleId(prismaEntity.insurance_line_cia_sale_id)
            .withCustomerId(prismaEntity.customer_id)
            .withAccountId(prismaEntity.account_id)
            .build();
    }
};
exports.PrismaSaleRepository = PrismaSaleRepository;
exports.PrismaSaleRepository = PrismaSaleRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PrismaSaleRepository);
//# sourceMappingURL=prisma-sale.repository.js.map