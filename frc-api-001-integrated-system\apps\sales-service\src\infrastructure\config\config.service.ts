import { Injectable, Inject } from '@nestjs/common';

/**
 * Servicio para acceder a las variables de configuración
 * Proporciona métodos para obtener valores específicos de configuración
 */
@Injectable()
export class ConfigService {
  constructor(@Inject('CONFIG') private readonly config: any) {}

  /**
   * Obtiene el host del servicio de ventas
   */
  get salesServiceHost(): string {
    return this.config.salesServiceHost;
  }

  /**
   * Obtiene el puerto del servicio de ventas
   */
  get salesServicePort(): number {
    return this.config.salesServicePort;
  }

  /**
   * Obtiene la clave secreta para JWT
   */
  get jwtAccessSecret(): string {
    return this.config.jwtAccessSecret;
  }

  /**
   * Obtiene la palabra secreta del servicio
   */
  get secretWord(): string {
    return this.config.secretWord;
  }
}
