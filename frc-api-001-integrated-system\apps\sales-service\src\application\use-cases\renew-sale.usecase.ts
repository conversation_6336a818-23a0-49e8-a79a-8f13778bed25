import { Inject } from "@nestjs/common";
import { RENEW_SALE_TRANSACTION, IRenewSaleTransaction } from "../../domain/ports/transactions/renew-sale.transaction";
import { RenewSaleDto } from "../../domain/ports/transactions/dtos/renew-sale.dto";
import { Sale } from "../../domain/models/sale";
import { JWT_SERVICE, IJwtService } from "../../domain/ports/services/jwt.service";

export const RENEW_SALE_USE_CASE = Symbol('RENEW_SALE_USE_CASE');
export class RenewSaleUseCase {
  constructor(
    @Inject(RENEW_SALE_TRANSACTION)
    private readonly _renewSaleTransaction: IRenewSaleTransaction,
    @Inject(JWT_SERVICE)
    private readonly _jwtService: IJwtService
  ) {}

  async execute(originalSaleId: number, renewalData: any, token: string): Promise<Sale> {
    // Extraer la información del usuario del token JWT
    const userInfo = await this._jwtService.verify(token);
    
    // Crear el DTO para la transacción
    const renewSaleDto: RenewSaleDto = {
      originalSaleId,
      newSale: {
        net_amount: renewalData.netAmount,
        total_amount: renewalData.totalAmount
      },
      startDate: new Date(renewalData.startDate),
      endDate: new Date(renewalData.endDate),
      accountId: userInfo.accountId,
      keepCustomerInfo: renewalData.keepCustomerInfo !== undefined ? renewalData.keepCustomerInfo : true
    };
    
    // Ejecutar la transacción
    return await this._renewSaleTransaction.run(renewSaleDto);
  }
}
