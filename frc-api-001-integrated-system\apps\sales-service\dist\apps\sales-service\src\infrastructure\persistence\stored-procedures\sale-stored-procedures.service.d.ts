import { SalesServicePrismaService } from '../prisma.service';
import { Sale } from '../../../domain/models/sale';
import { SaleBuilder } from '../../../domain/builders/sale.builder';
import { Decimal } from '@prisma/client/runtime/library';
import { ISaleStoredProcedures } from './sale-stored-procedures.interface';
export declare class SaleStoredProceduresService implements ISaleStoredProcedures {
    private readonly prismaService;
    private readonly saleBuilder;
    constructor(prismaService: SalesServicePrismaService, saleBuilder: SaleBuilder);
    createCompleteSale(saleData: {
        policy_number?: string;
        start_sale_date?: Date;
        end_sale_date?: Date;
        net_amount?: number | Decimal;
        total_amount?: number | Decimal;
        condition_sale_id?: number;
        currency_id?: number;
        cia_company_id?: number;
        insurance_line_cia_sale_id?: number;
        account_id: number;
    }, customerData: {
        full_name: string;
        doi?: string;
        phone_number?: string;
        mobile_number?: string;
        document_type_id: number;
    }, locationData?: {
        address?: string;
        department?: string;
        province?: string;
        district?: string;
    }): Promise<Sale>;
    updateCompleteSale(saleId: number, saleData: {
        policy_number?: string;
        start_sale_date?: Date;
        end_sale_date?: Date;
        net_amount?: number | Decimal;
        total_amount?: number | Decimal;
        condition_sale_id?: number;
        currency_id?: number;
        cia_company_id?: number;
        insurance_line_cia_sale_id?: number;
        account_id?: number;
    }, customerData?: {
        full_name?: string;
        doi?: string;
        phone_number?: string;
        mobile_number?: string;
        document_type_id?: number;
    }, locationData?: {
        address?: string;
        department?: string;
        province?: string;
        district?: string;
    }): Promise<Sale>;
    renewSale(originalSaleId: number, newSaleData: {
        policy_number?: string;
        net_amount?: number | Decimal;
        total_amount?: number | Decimal;
        condition_sale_id?: number;
        currency_id?: number;
        cia_company_id?: number;
        insurance_line_cia_sale_id?: number;
    }, startDate: Date, endDate: Date, keepCustomerInfo: boolean | undefined, accountId: number): Promise<Sale>;
    getSalesWithFilters(filters: {
        customerId?: number;
        customerDoi?: string;
        customerName?: string;
        policyNumber?: string;
        startDateFrom?: Date;
        startDateTo?: Date;
        endDateFrom?: Date;
        endDateTo?: Date;
        accountId?: number;
        conditionSaleId?: number;
        currencyId?: number;
        ciaCompanyId?: number;
        insuranceLineCiaSaleId?: number;
    }, pagination?: {
        skip?: number;
        take?: number;
        orderBy?: string;
        orderDirection?: 'asc' | 'desc';
    }): Promise<{
        sales: Sale[];
        total: number;
    }>;
    private createCustomerWithLocation;
    private mapSaleToDomain;
}
