import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { WebModule } from './infrastructure/web/web.module';
import { HttpExceptionFilter } from './infrastructure/web/filters/http-exception.filter';
import { ConfigModule } from './infrastructure/config';
import { ServicesModule } from './infrastructure/services/services.module';

/**
 * Módulo principal de la aplicación de servicio de ventas
 * Integra todos los submódulos y configura filtros globales
 */
@Module({
  imports: [
    ConfigModule,
    WebModule,
    ServicesModule
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter
    }
  ],
})
export class SalesServiceModule {}
