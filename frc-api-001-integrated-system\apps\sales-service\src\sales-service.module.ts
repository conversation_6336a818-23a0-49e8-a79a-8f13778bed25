import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { WebModule } from './infrastructure/web/web.module';
import { HttpExceptionFilter } from './infrastructure/web/filters/http-exception.filter';
import { ConfigModule } from './infrastructure/config';
import { ServicesModule } from './infrastructure/services/services.module';

// Casos de uso
import { CreateSaleUseCase, CREATE_SALE_USE_CASE } from './application/use-cases/create-sale.usecase';
import { UpdateSaleUseCase, UPDATE_SALE_USE_CASE } from './application/use-cases/update-sale.usecase';
import { GetSaleByIdUseCase, GET_SALE_BY_ID_USE_CASE } from './application/use-cases/get-sale-by-id.usecase';
import { GetSalesByPageUseCase, GET_SALES_BY_PAGE_USE_CASE } from './application/use-cases/get-sales-by-page.usecase';
import { RenewSaleUseCase, RENEW_SALE_USE_CASE } from './application/use-cases/renew-sale.usecase';
import { DeleteSaleUseCase, DELETE_SALE_USE_CASE } from './application/use-cases/delete-sale.usecase';

// Transacciones
import { PrismaCreateSaleTransaction } from './infrastructure/persistence/transactions/prisma-create-sale.transaction';
import { PrismaUpdateSaleTransaction } from './infrastructure/persistence/transactions/prisma-update-sale.transaction';
import { PrismaDeleteSaleTransaction } from './infrastructure/persistence/transactions/prisma-delete-sale.transaction';
import { PrismaRenewSaleTransaction } from './infrastructure/persistence/transactions/prisma-renew-sale.transaction';
import { CREATE_SALE_TRANSACTION } from './domain/ports/transactions/create-sale.transaction';
import { UPDATE_SALE_TRANSACTION } from './domain/ports/transactions/update-sale.transaction';
import { DELETE_SALE_TRANSACTION } from './domain/ports/transactions/delete-sale.transaction';
import { RENEW_SALE_TRANSACTION } from './domain/ports/transactions/renew-sale.transaction';

// Repositorios
import { PrismaCustomerRepository } from './infrastructure/persistence/repositories/prisma-customer.repository';
import { PrismaLocationRepository } from './infrastructure/persistence/repositories/prisma-location.repository';
import { PrismaSaleRepository } from './infrastructure/persistence/repositories/prisma-sale.repository';
import { CUSTOMER_REPOSITORY } from './domain/ports/repositories/customer.repository';
import { LOCATION_REPOSITORY } from './domain/ports/repositories/location.repository';
import { SALE_REPOSITORY } from './domain/ports/repositories/sale.repository';

// Servicios
import { AuthService } from './infrastructure/services/auth/auth.service';
import { JWT_SERVICE } from './domain/ports/services/jwt.service';

/**
 * Módulo principal de la aplicación de servicio de ventas
 * Integra todos los submódulos y configura filtros globales
 */
@Module({
  imports: [
    ConfigModule,
    WebModule,
    ServicesModule
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter
    },
    // Transacciones
    {
      provide: CREATE_SALE_TRANSACTION,
      useClass: PrismaCreateSaleTransaction
    },
    {
      provide: UPDATE_SALE_TRANSACTION,
      useClass: PrismaUpdateSaleTransaction
    },
    {
      provide: DELETE_SALE_TRANSACTION,
      useClass: PrismaDeleteSaleTransaction
    },
    {
      provide: RENEW_SALE_TRANSACTION,
      useClass: PrismaRenewSaleTransaction
    },
    // Repositorios
    {
      provide: CUSTOMER_REPOSITORY,
      useClass: PrismaCustomerRepository
    },
    {
      provide: LOCATION_REPOSITORY,
      useClass: PrismaLocationRepository
    },
    {
      provide: SALE_REPOSITORY,
      useClass: PrismaSaleRepository
    },
    // Servicios
    {
      provide: JWT_SERVICE,
      useFactory: (authService: AuthService) => ({
        verify: async (token: string) => {
          const payload = authService.verifyToken(token);
          return {
            accountId: payload.accountId,
            roleId: payload.roleId,
            officeId: payload.officeId
          };
        }
      }),
      inject: [AuthService]
    },
    // Casos de uso
    {
      provide: CREATE_SALE_USE_CASE,
      useClass: CreateSaleUseCase
    },
    {
      provide: UPDATE_SALE_USE_CASE,
      useClass: UpdateSaleUseCase
    },
    {
      provide: GET_SALE_BY_ID_USE_CASE,
      useClass: GetSaleByIdUseCase
    },
    {
      provide: GET_SALES_BY_PAGE_USE_CASE,
      useClass: GetSalesByPageUseCase
    },
    {
      provide: RENEW_SALE_USE_CASE,
      useClass: RenewSaleUseCase
    },
    {
      provide: DELETE_SALE_USE_CASE,
      useClass: DeleteSaleUseCase
    }
  ],
})
export class SalesServiceModule {}
