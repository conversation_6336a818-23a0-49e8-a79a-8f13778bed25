import { IsString, IsN<PERSON>ber, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO para filtrar y paginar ventas
 */
export class FilterSalesRequestDto {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;

  @IsString()
  @IsOptional()
  policy_number?: string;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  start_date?: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  end_date?: Date;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  customer_id?: number;

  @IsString()
  @IsOptional()
  customer_name?: string;

  @IsString()
  @IsOptional()
  customer_doi?: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  cia_company_id?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  insurance_line_id?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  condition_sale_id?: number;
}
