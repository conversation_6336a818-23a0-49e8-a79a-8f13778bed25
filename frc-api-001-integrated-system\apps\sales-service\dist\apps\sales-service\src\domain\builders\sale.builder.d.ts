import { Sale } from "../models/sale";
export declare class SaleBuilder {
    private sale;
    constructor();
    reset(): void;
    build(): Sale;
    withSaleId(saleId: number): this;
    withPolicyNumber(policyNumber?: string): this;
    withStartSaleDate(startSaleDate?: Date): this;
    withEndSaleDate(endSaleDate?: Date): this;
    withNetAmount(netAmount?: number): this;
    withTotalAmount(totalAmount?: number): this;
    withCustomerId(customerId: number): this;
    withAccountId(accountId: number): this;
    withConditionSaleId(conditionSaleId?: number): this;
    withCurrencyId(currencyId?: number): this;
    withCiaCompanyId(ciaCompanyId?: number): this;
    withInsuranceLineCiaSaleId(insuranceLineCiaSaleId?: number): this;
}
