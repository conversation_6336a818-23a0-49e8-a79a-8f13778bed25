import { Location } from "../models/location";

export class LocationBuilder {
  private location: Location;

  constructor() {
    this.reset();
  }

  reset(): void {
    this.location = new Location();
  }

  build(): Location {
    const builtLocation = this.location;
    this.reset();
    return builtLocation;
  }

  withLocationId(locationId: number): this {
    this.location.location_id = locationId;
    return this;
  }

  withAddress(address: string): this {
    this.location.address = address;
    return this;
  }

  withDepartment(department: string): this {
    this.location.department = department;
    return this;
  }

  withProvince(province: string): this {
    this.location.province = province;
    return this;
  }

  withDistrict(district: string): this {
    this.location.district = district;
    return this;
  }
}
