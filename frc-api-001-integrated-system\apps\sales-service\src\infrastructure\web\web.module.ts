import { Module } from '@nestjs/common';
import { SaleController } from './controllers';
import { ServicesModule } from '../services/services.module';
import { GuardsModule } from './guards/guards.module';

/**
 * Módulo web para el servicio de ventas
 * Registra controladores, filtros y otros componentes relacionados con la capa web
 */
@Module({
  imports: [
    ServicesModule,
    GuardsModule
  ],
  controllers: [
    SaleController
  ],
  providers: [],
  exports: []
})
export class WebModule {}
