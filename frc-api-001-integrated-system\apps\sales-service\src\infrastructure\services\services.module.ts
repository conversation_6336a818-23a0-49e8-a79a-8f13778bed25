import { Module } from '@nestjs/common';
import { SaleService } from './sale/sale.service';
import { StoredProceduresModule } from '../persistence/stored-procedures/stored-procedures.module';
import { PrismaModule } from '../../../../../libs/prisma/prisma.module';
import { SaleBuilder } from '../../domain/builders/sale.builder';
import { AuthService } from './auth/auth.service';
import { ConfigModule } from '../config';

/**
 * Módulo de servicios de infraestructura
 * Registra los servicios que actúan como puente entre los casos de uso y la capa de persistencia
 */
@Module({
  imports: [
    PrismaModule,
    StoredProceduresModule,
    ConfigModule
  ],
  providers: [
    SaleService,
    SaleBuilder,
    AuthService
  ],
  exports: [
    SaleService,
    AuthService
  ]
})
export class ServicesModule {}
