import { Sale } from '../../../models/sale';

export class RenewSaleDto {
  originalSaleId: number;    // ID de la venta original a renovar
  newSale: Partial<Sale>;    // Datos específicos para la nueva venta (sobrescribirán los de la original)
  startDate: Date;           // Nueva fecha de inicio
  endDate: Date;             // Nueva fecha de fin
  accountId: number;         // ID del usuario que está renovando la venta
  keepCustomerInfo: boolean; // Si es true, mantiene la misma información del cliente
}
