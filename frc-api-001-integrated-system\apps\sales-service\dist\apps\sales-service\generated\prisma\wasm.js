
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AccountScalarFieldEnum = {
  account_id: 'account_id',
  nickname: 'nickname',
  password: 'password',
  is_active: 'is_active',
  user_id: 'user_id',
  role_id: 'role_id',
  office_id: 'office_id'
};

exports.Prisma.ApplicationScalarFieldEnum = {
  application_id: 'application_id',
  application_name: 'application_name'
};

exports.Prisma.CiaCompanyScalarFieldEnum = {
  cia_company_id: 'cia_company_id',
  company_name: 'company_name'
};

exports.Prisma.ConditionSaleScalarFieldEnum = {
  condition_sale_id: 'condition_sale_id',
  condition_name: 'condition_name'
};

exports.Prisma.CurrencyScalarFieldEnum = {
  currency_id: 'currency_id',
  currency_type: 'currency_type'
};

exports.Prisma.CustomerScalarFieldEnum = {
  customer_id: 'customer_id',
  full_name: 'full_name',
  doi: 'doi',
  phone_number: 'phone_number',
  mobile_number: 'mobile_number',
  document_type_id: 'document_type_id',
  location_id: 'location_id'
};

exports.Prisma.DocumentTypeScalarFieldEnum = {
  document_type_id: 'document_type_id',
  document_name: 'document_name'
};

exports.Prisma.InsuranceLineCiaScalarFieldEnum = {
  insurance_line_cia_id: 'insurance_line_cia_id',
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineCiaSaleScalarFieldEnum = {
  insurance_line_cia_sale_id: 'insurance_line_cia_sale_id',
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineCodeScalarFieldEnum = {
  insurance_line_code_id: 'insurance_line_code_id',
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineSbsScalarFieldEnum = {
  insurance_line_sbs_id: 'insurance_line_sbs_id',
  insurance_name: 'insurance_name'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  invoice_id: 'invoice_id',
  invoice_date: 'invoice_date',
  invoice_number: 'invoice_number',
  policy_number: 'policy_number',
  receipt_number: 'receipt_number',
  liquidation: 'liquidation',
  net_amount: 'net_amount',
  commission_amount: 'commission_amount',
  currency_id: 'currency_id',
  cia_company_id: 'cia_company_id',
  insurance_line_sbs_id: 'insurance_line_sbs_id',
  insurance_line_cia_id: 'insurance_line_cia_id',
  insurance_line_code_id: 'insurance_line_code_id',
  customer_id: 'customer_id',
  account_id: 'account_id'
};

exports.Prisma.LocationScalarFieldEnum = {
  location_id: 'location_id',
  address: 'address',
  department: 'department',
  province: 'province',
  district: 'district'
};

exports.Prisma.OfficeScalarFieldEnum = {
  office_id: 'office_id',
  office_location: 'office_location'
};

exports.Prisma.PermissionScalarFieldEnum = {
  application_id: 'application_id',
  account_id: 'account_id',
  committed_at: 'committed_at'
};

exports.Prisma.RoleScalarFieldEnum = {
  role_id: 'role_id',
  role_name: 'role_name'
};

exports.Prisma.SaleScalarFieldEnum = {
  sale_id: 'sale_id',
  policy_number: 'policy_number',
  start_sale_date: 'start_sale_date',
  end_sale_date: 'end_sale_date',
  net_amount: 'net_amount',
  total_amount: 'total_amount',
  customer_id: 'customer_id',
  account_id: 'account_id',
  condition_sale_id: 'condition_sale_id',
  currency_id: 'currency_id',
  cia_company_id: 'cia_company_id',
  insurance_line_cia_sale_id: 'insurance_line_cia_sale_id'
};

exports.Prisma.UserScalarFieldEnum = {
  user_id: 'user_id',
  names: 'names',
  lastnames: 'lastnames',
  doi: 'doi',
  email: 'email',
  mobile_number: 'mobile_number'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.AccountOrderByRelevanceFieldEnum = {
  nickname: 'nickname',
  password: 'password'
};

exports.Prisma.ApplicationOrderByRelevanceFieldEnum = {
  application_name: 'application_name'
};

exports.Prisma.CiaCompanyOrderByRelevanceFieldEnum = {
  company_name: 'company_name'
};

exports.Prisma.ConditionSaleOrderByRelevanceFieldEnum = {
  condition_name: 'condition_name'
};

exports.Prisma.CurrencyOrderByRelevanceFieldEnum = {
  currency_type: 'currency_type'
};

exports.Prisma.CustomerOrderByRelevanceFieldEnum = {
  full_name: 'full_name',
  doi: 'doi',
  phone_number: 'phone_number',
  mobile_number: 'mobile_number'
};

exports.Prisma.DocumentTypeOrderByRelevanceFieldEnum = {
  document_name: 'document_name'
};

exports.Prisma.InsuranceLineCiaOrderByRelevanceFieldEnum = {
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineCiaSaleOrderByRelevanceFieldEnum = {
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineCodeOrderByRelevanceFieldEnum = {
  insurance_name: 'insurance_name'
};

exports.Prisma.InsuranceLineSbsOrderByRelevanceFieldEnum = {
  insurance_name: 'insurance_name'
};

exports.Prisma.InvoiceOrderByRelevanceFieldEnum = {
  invoice_number: 'invoice_number',
  policy_number: 'policy_number',
  receipt_number: 'receipt_number'
};

exports.Prisma.LocationOrderByRelevanceFieldEnum = {
  address: 'address',
  department: 'department',
  province: 'province',
  district: 'district'
};

exports.Prisma.OfficeOrderByRelevanceFieldEnum = {
  office_location: 'office_location'
};

exports.Prisma.RoleOrderByRelevanceFieldEnum = {
  role_name: 'role_name'
};

exports.Prisma.SaleOrderByRelevanceFieldEnum = {
  policy_number: 'policy_number'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  names: 'names',
  lastnames: 'lastnames',
  doi: 'doi',
  email: 'email',
  mobile_number: 'mobile_number'
};


exports.Prisma.ModelName = {
  Account: 'Account',
  Application: 'Application',
  CiaCompany: 'CiaCompany',
  ConditionSale: 'ConditionSale',
  Currency: 'Currency',
  Customer: 'Customer',
  DocumentType: 'DocumentType',
  InsuranceLineCia: 'InsuranceLineCia',
  InsuranceLineCiaSale: 'InsuranceLineCiaSale',
  InsuranceLineCode: 'InsuranceLineCode',
  InsuranceLineSbs: 'InsuranceLineSbs',
  Invoice: 'Invoice',
  Location: 'Location',
  Office: 'Office',
  Permission: 'Permission',
  Role: 'Role',
  Sale: 'Sale',
  User: 'User'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
