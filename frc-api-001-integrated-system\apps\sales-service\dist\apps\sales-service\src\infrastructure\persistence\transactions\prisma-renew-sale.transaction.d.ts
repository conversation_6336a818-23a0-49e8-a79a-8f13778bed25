import { SalesServicePrismaService } from "../prisma.service";
import { IRenewSaleTransaction } from "../../../domain/ports/transactions/renew-sale.transaction";
import { RenewSaleDto } from "../../../domain/ports/transactions/dtos/renew-sale.dto";
import { Sale } from "../../../domain/models/sale";
export declare class PrismaRenewSaleTransaction implements IRenewSaleTransaction {
    private readonly _prismaService;
    private readonly saleBuilder;
    constructor(_prismaService: SalesServicePrismaService);
    run(renewSaleDto: RenewSaleDto): Promise<Sale>;
}
