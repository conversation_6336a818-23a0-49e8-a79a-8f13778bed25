// Exportación de todos los procedimientos almacenados
// Nota: Las importaciones y exportaciones se manejan directamente en los módulos que necesitan estos servicios
// Este archivo sirve como punto de entrada para la documentación

// Para usar los procedimientos almacenados, importar:
// import { SALE_STORED_PROCEDURES, ISaleStoredProcedures } from '../infrastructure/persistence/stored-procedures/sale-stored-procedures.interface';
// import { StoredProceduresModule } from '../infrastructure/persistence/stored-procedures/stored-procedures.module';

// Y luego inyectar usando:
// constructor(@Inject(SALE_STORED_PROCEDURES) private readonly saleStoredProcedures: ISaleStoredProcedures) {}
