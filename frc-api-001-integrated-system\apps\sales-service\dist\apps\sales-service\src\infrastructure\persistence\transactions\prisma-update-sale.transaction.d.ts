import { SalesServicePrismaService } from "../prisma.service";
import { IUpdateSaleTransaction } from "../../../domain/ports/transactions/update-sale.transaction";
import { UpdateSaleDto } from "../../../domain/ports/transactions/dtos/update-sale.dto";
import { Sale } from "../../../domain/models/sale";
export declare class PrismaUpdateSaleTransaction implements IUpdateSaleTransaction {
    private readonly _prismaService;
    private readonly saleBuilder;
    constructor(_prismaService: SalesServicePrismaService);
    run(updateSaleDto: UpdateSaleDto): Promise<Sale>;
}
