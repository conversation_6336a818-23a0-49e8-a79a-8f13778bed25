"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleService = void 0;
const common_1 = require("@nestjs/common");
const sale_1 = require("../../../domain/models/sale");
const sale_stored_procedures_interface_1 = require("../../persistence/stored-procedures/sale-stored-procedures.interface");
const prisma_service_1 = require("../../../../../../libs/prisma/prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let SaleService = class SaleService {
    saleStoredProcedures;
    prismaService;
    saleBuilder;
    constructor(saleStoredProcedures, prismaService, saleBuilder) {
        this.saleStoredProcedures = saleStoredProcedures;
        this.prismaService = prismaService;
        this.saleBuilder = saleBuilder;
    }
    async createSale(createSaleDto) {
        const { sale: saleData, customer, location, accountId } = createSaleDto;
        return this.saleStoredProcedures.createCompleteSale({
            policy_number: saleData.policy_number,
            start_sale_date: saleData.start_sale_date,
            end_sale_date: saleData.end_sale_date,
            net_amount: saleData.net_amount,
            total_amount: saleData.total_amount,
            condition_sale_id: saleData.condition_sale_id,
            currency_id: saleData.currency_id,
            cia_company_id: saleData.cia_company_id,
            insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id,
            account_id: accountId
        }, {
            full_name: customer.full_name,
            doi: customer.doi,
            phone_number: customer.phone_number,
            mobile_number: customer.mobile_number,
            document_type_id: customer.document_type_id
        }, location ? {
            address: location.address,
            department: location.department,
            province: location.province,
            district: location.district
        } : undefined);
    }
    async updateSale(updateSaleDto) {
        const { saleId, sale: saleData, customer, location, accountId } = updateSaleDto;
        return this.saleStoredProcedures.updateCompleteSale(saleId, {
            policy_number: saleData.policy_number,
            start_sale_date: saleData.start_sale_date,
            end_sale_date: saleData.end_sale_date,
            net_amount: saleData.net_amount,
            total_amount: saleData.total_amount,
            condition_sale_id: saleData.condition_sale_id,
            currency_id: saleData.currency_id,
            cia_company_id: saleData.cia_company_id,
            insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id,
            account_id: accountId
        }, customer ? {
            full_name: customer.full_name,
            doi: customer.doi,
            phone_number: customer.phone_number,
            mobile_number: customer.mobile_number,
            document_type_id: customer.document_type_id
        } : undefined, location ? {
            address: location.address,
            department: location.department,
            province: location.province,
            district: location.district
        } : undefined);
    }
    async renewSale(originalSaleId, newSaleData, startDate, endDate, keepCustomerInfo, accountId) {
        return this.saleStoredProcedures.renewSale(originalSaleId, {
            policy_number: newSaleData.policy_number,
            net_amount: newSaleData.net_amount,
            total_amount: newSaleData.total_amount,
            condition_sale_id: newSaleData.condition_sale_id,
            currency_id: newSaleData.currency_id,
            cia_company_id: newSaleData.cia_company_id,
            insurance_line_cia_sale_id: newSaleData.insurance_line_cia_sale_id
        }, startDate, endDate, keepCustomerInfo, accountId);
    }
    async getSaleById(id) {
        const sale = await this.prismaService.sale.findUnique({
            where: { sale_id: id },
            include: {
                Customer: {
                    include: {
                        Location: true
                    }
                }
            }
        });
        if (!sale) {
            return null;
        }
        const domainSale = new sale_1.Sale();
        domainSale.sale_id = sale.sale_id;
        domainSale.policy_number = sale.policy_number || undefined;
        domainSale.start_sale_date = sale.start_sale_date || undefined;
        domainSale.end_sale_date = sale.end_sale_date || undefined;
        domainSale.net_amount = sale.net_amount ? Number(sale.net_amount) : undefined;
        domainSale.total_amount = sale.total_amount ? Number(sale.total_amount) : undefined;
        domainSale.condition_sale_id = sale.condition_sale_id || undefined;
        domainSale.currency_id = sale.currency_id || undefined;
        domainSale.cia_company_id = sale.cia_company_id || undefined;
        domainSale.insurance_line_cia_sale_id = sale.insurance_line_cia_sale_id || undefined;
        domainSale.account_id = sale.account_id;
        domainSale.customer_id = sale.customer_id;
        return domainSale;
    }
    async getSalesWithFilters(filters, pagination) {
        return this.saleStoredProcedures.getSalesWithFilters(filters, pagination);
    }
    async deleteSale(id) {
        try {
            await this.prismaService.sale.delete({
                where: { sale_id: id }
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
};
exports.SaleService = SaleService;
exports.SaleService = SaleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(sale_stored_procedures_interface_1.SALE_STORED_PROCEDURES)),
    __metadata("design:paramtypes", [Object, prisma_service_1.PrismaService,
        sale_builder_1.SaleBuilder])
], SaleService);
//# sourceMappingURL=sale.service.js.map