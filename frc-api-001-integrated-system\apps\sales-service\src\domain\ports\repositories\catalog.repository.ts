import { CiaCompany } from '../../models/cia-company';
import { ConditionSale } from '../../models/condition-sale';
import { Currency } from '../../models/currency';
import { DocumentType } from '../../models/document-type';
import { InsuranceLineCiaSale } from '../../models/insurance-line-cia-sale';

export const CATALOG_REPOSITORY = Symbol('CATALOG_REPOSITORY');
export interface ICatalogRepository {
  // Compañías de seguros
  getAllCiaCompanies(): Promise<CiaCompany[]>;
  getCiaCompanyById(id: number): Promise<CiaCompany | null>;
  
  // Condiciones de venta
  getAllConditionSales(): Promise<ConditionSale[]>;
  getConditionSaleById(id: number): Promise<ConditionSale | null>;
  
  // Monedas
  getAllCurrencies(): Promise<Currency[]>;
  getCurrencyById(id: number): Promise<Currency | null>;
  
  // Tipos de documento
  getAllDocumentTypes(): Promise<DocumentType[]>;
  getDocumentTypeById(id: number): Promise<DocumentType | null>;
  
  // Ramos de seguro
  getAllInsuranceLineCiaSales(): Promise<InsuranceLineCiaSale[]>;
  getInsuranceLineCiaSaleById(id: number): Promise<InsuranceLineCiaSale | null>;
}
