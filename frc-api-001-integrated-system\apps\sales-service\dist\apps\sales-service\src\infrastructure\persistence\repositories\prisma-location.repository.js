"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaLocationRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const location_builder_1 = require("../../../domain/builders/location.builder");
let PrismaLocationRepository = class PrismaLocationRepository {
    _prismaService;
    locationBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.locationBuilder = new location_builder_1.LocationBuilder();
    }
    async findAll() {
        const locations = await this._prismaService.location.findMany();
        return locations.map(location => this.mapPrismaToLocation(location));
    }
    async findById(id) {
        const location = await this._prismaService.location.findUnique({
            where: { location_id: id }
        });
        if (!location)
            return null;
        return this.mapPrismaToLocation(location);
    }
    async create(location) {
        const createdLocation = await this._prismaService.location.create({
            data: {
                address: location.address,
                department: location.department,
                province: location.province,
                district: location.district
            }
        });
        return this.mapPrismaToLocation(createdLocation);
    }
    async update(id, location) {
        const updatedLocation = await this._prismaService.location.update({
            where: { location_id: id },
            data: {
                address: location.address,
                department: location.department,
                province: location.province,
                district: location.district
            }
        });
        return this.mapPrismaToLocation(updatedLocation);
    }
    async delete(id) {
        await this._prismaService.location.delete({
            where: { location_id: id }
        });
    }
    async getAllDepartments() {
        const result = await this._prismaService.location.findMany({
            distinct: ['department'],
            select: { department: true }
        });
        return result
            .map(item => item.department)
            .filter(department => department !== null && department !== undefined);
    }
    async getProvincesByDepartment(department) {
        const result = await this._prismaService.location.findMany({
            where: { department },
            distinct: ['province'],
            select: { province: true }
        });
        return result
            .map(item => item.province)
            .filter(province => province !== null && province !== undefined);
    }
    async getDistrictsByProvince(province) {
        const result = await this._prismaService.location.findMany({
            where: { province },
            distinct: ['district'],
            select: { district: true }
        });
        return result
            .map(item => item.district)
            .filter(district => district !== null && district !== undefined);
    }
    mapPrismaToLocation(prismaEntity) {
        return this.locationBuilder
            .withLocationId(prismaEntity.location_id)
            .withAddress(prismaEntity.address)
            .withDepartment(prismaEntity.department)
            .withProvince(prismaEntity.province)
            .withDistrict(prismaEntity.district)
            .build();
    }
};
exports.PrismaLocationRepository = PrismaLocationRepository;
exports.PrismaLocationRepository = PrismaLocationRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaLocationRepository);
//# sourceMappingURL=prisma-location.repository.js.map