import { ICreateSaleTransaction } from "../../domain/ports/transactions/create-sale.transaction";
import { Sale } from "../../domain/models/sale";
import { IJwtService } from "../../domain/ports/services/jwt.service";
export declare const CREATE_SALE_USE_CASE: unique symbol;
export declare class CreateSaleUseCase {
    private readonly _createSaleTransaction;
    private readonly _jwtService;
    private readonly saleBuilder;
    private readonly customerBuilder;
    private readonly locationBuilder;
    constructor(_createSaleTransaction: ICreateSaleTransaction, _jwtService: IJwtService);
    execute(saleData: any, token: string): Promise<Sale>;
}
