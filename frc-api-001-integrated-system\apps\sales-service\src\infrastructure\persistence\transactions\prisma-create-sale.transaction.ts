import { Injectable } from "@nestjs/common";
import { SalesServicePrismaService } from "../prisma.service";
import { ICreateSaleTransaction } from "../../../domain/ports/transactions/create-sale.transaction";
import { CreateSaleDto } from "../../../domain/ports/transactions/dtos/create-sale.dto";
import { Sale } from "../../../domain/models/sale";
import { SaleBuilder } from "../../../domain/builders/sale.builder";

@Injectable()
export class PrismaCreateSaleTransaction implements ICreateSaleTransaction {
  private readonly saleBuilder: SaleBuilder;

  constructor(
    private readonly _prismaService: SalesServicePrismaService,
  ) {
    this.saleBuilder = new SaleBuilder();
  }

  async run(createSaleDto: CreateSaleDto): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this._prismaService.$transaction(async (prisma) => {
      // 1. Verificar si el cliente ya existe por DOI
      let existingCustomer: any = null;
      if (createSaleDto.customer.doi) {
        existingCustomer = await prisma.customer.findUnique({
          where: { doi: createSaleDto.customer.doi }
        });
      }
      
      // 2. Crear la ubicación si es necesario
      let locationId: number | undefined = undefined;
      
      if (createSaleDto.location) {
        const locationData = {
          address: createSaleDto.location.address,
          department: createSaleDto.location.department,
          province: createSaleDto.location.province,
          district: createSaleDto.location.district
        };
        
        // Crear nueva ubicación
        const location = await prisma.location.create({
          data: locationData
        });
        
        locationId = location.location_id;
      }

      // 3. Crear o actualizar el cliente
      let customer;
      if (existingCustomer) {
        // Actualizar cliente existente
        customer = await prisma.customer.update({
          where: { customer_id: existingCustomer.customer_id },
          data: {
            full_name: createSaleDto.customer.full_name,
            phone_number: createSaleDto.customer.phone_number,
            mobile_number: createSaleDto.customer.mobile_number,
            document_type_id: createSaleDto.customer.document_type_id,
            location_id: locationId || existingCustomer.location_id
          }
        });
      } else {
        // Crear nuevo cliente
        customer = await prisma.customer.create({
          data: {
            full_name: createSaleDto.customer.full_name,
            doi: createSaleDto.customer.doi,
            phone_number: createSaleDto.customer.phone_number,
            mobile_number: createSaleDto.customer.mobile_number,
            document_type_id: createSaleDto.customer.document_type_id,
            location_id: locationId
          }
        });
      }

      // 4. Crear la venta
      const sale = await prisma.sale.create({
        data: {
          policy_number: createSaleDto.sale.policy_number || undefined,
          start_sale_date: createSaleDto.sale.start_sale_date || undefined,
          end_sale_date: createSaleDto.sale.end_sale_date || undefined,
          net_amount: createSaleDto.sale.net_amount as any,
          total_amount: createSaleDto.sale.total_amount as any,
          condition_sale_id: createSaleDto.sale.condition_sale_id || undefined,
          currency_id: createSaleDto.sale.currency_id || undefined,
          cia_company_id: createSaleDto.sale.cia_company_id || undefined,
          insurance_line_cia_sale_id: createSaleDto.sale.insurance_line_cia_sale_id || undefined,
          customer_id: customer.customer_id,
          account_id: createSaleDto.accountId
        }
      });

      // 5. Mapear la entidad Prisma a un modelo de dominio
      return this.saleBuilder
        .withSaleId(sale.sale_id)
        .withPolicyNumber(sale.policy_number || undefined)
        .withStartSaleDate(sale.start_sale_date || undefined)
        .withEndSaleDate(sale.end_sale_date || undefined)
        .withNetAmount(Number(sale.net_amount) || 0)
        .withTotalAmount(Number(sale.total_amount) || 0)
        .withConditionSaleId(sale.condition_sale_id || undefined)
        .withCurrencyId(sale.currency_id || undefined)
        .withCiaCompanyId(sale.cia_company_id || undefined)
        .withInsuranceLineCiaSaleId(sale.insurance_line_cia_sale_id || undefined)
        .withCustomerId(sale.customer_id)
        .withAccountId(sale.account_id)
        .build();
    });
  }
}
