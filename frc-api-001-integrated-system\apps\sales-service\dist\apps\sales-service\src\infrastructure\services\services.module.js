"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicesModule = void 0;
const common_1 = require("@nestjs/common");
const sale_service_1 = require("./sale/sale.service");
const stored_procedures_module_1 = require("../persistence/stored-procedures/stored-procedures.module");
const prisma_module_1 = require("../../../../../libs/prisma/prisma.module");
const sale_builder_1 = require("../../domain/builders/sale.builder");
const auth_service_1 = require("./auth/auth.service");
const config_1 = require("../config");
let ServicesModule = class ServicesModule {
};
exports.ServicesModule = ServicesModule;
exports.ServicesModule = ServicesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            stored_procedures_module_1.StoredProceduresModule,
            config_1.ConfigModule
        ],
        providers: [
            sale_service_1.SaleService,
            sale_builder_1.SaleBuilder,
            auth_service_1.AuthService
        ],
        exports: [
            sale_service_1.SaleService,
            auth_service_1.AuthService
        ]
    })
], ServicesModule);
//# sourceMappingURL=services.module.js.map