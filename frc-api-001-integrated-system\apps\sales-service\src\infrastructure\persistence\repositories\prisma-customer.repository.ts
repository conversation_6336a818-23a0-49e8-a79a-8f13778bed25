import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { CUSTOMER_REPOSITORY, ICustomerRepository } from "../../../domain/ports/repositories/customer.repository";
import { Customer } from "../../../domain/models/customer";
import { CustomerBuilder } from "../../../domain/builders/customer.builder";

@Injectable()
export class PrismaCustomerRepository implements ICustomerRepository {
  private readonly customerBuilder: CustomerBuilder;

  constructor(
    private readonly _prismaService: PrismaService,
  ) {
    this.customerBuilder = new CustomerBuilder();
  }

  async findAll(): Promise<Customer[]> {
    const customers = await this._prismaService.customer.findMany();
    return customers.map(customer => this.mapPrismaToCustomer(customer));
  }

  async findById(id: number): Promise<Customer | null> {
    const customer = await this._prismaService.customer.findUnique({
      where: { customer_id: id }
    });

    if (!customer) return null;

    return this.mapPrismaToCustomer(customer);
  }

  async findByDoi(doi: string): Promise<Customer | null> {
    const customer = await this._prismaService.customer.findFirst({
      where: { doi }
    });

    if (!customer) return null;

    return this.mapPrismaToCustomer(customer);
  }

  async create(customer: Customer): Promise<Customer> {
    const createdCustomer = await this._prismaService.customer.create({
      data: {
        full_name: customer.full_name,
        doi: customer.doi,
        phone_number: customer.phone_number,
        mobile_number: customer.mobile_number,
        document_type_id: customer.document_type_id
      }
    });

    return this.mapPrismaToCustomer(createdCustomer);
  }

  async update(id: number, customer: Customer): Promise<Customer> {
    const updatedCustomer = await this._prismaService.customer.update({
      where: { customer_id: id },
      data: {
        full_name: customer.full_name,
        doi: customer.doi,
        phone_number: customer.phone_number,
        mobile_number: customer.mobile_number,
        document_type_id: customer.document_type_id
      }
    });

    return this.mapPrismaToCustomer(updatedCustomer);
  }

  async delete(id: number): Promise<void> {
    await this._prismaService.customer.delete({
      where: { customer_id: id }
    });
  }

  // Método auxiliar para mapear entidades de Prisma a modelos de dominio
  private mapPrismaToCustomer(prismaEntity: any): Customer {
    return this.customerBuilder
      .withCustomerId(prismaEntity.customer_id)
      .withFullName(prismaEntity.full_name)
      .withDoi(prismaEntity.doi)
      .withPhoneNumber(prismaEntity.phone_number)
      .withMobileNumber(prismaEntity.mobile_number)
      .withDocumentTypeId(prismaEntity.document_type_id)
      .build();
  }
}
