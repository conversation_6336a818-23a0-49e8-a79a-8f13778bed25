"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoredProceduresModule = void 0;
const common_1 = require("@nestjs/common");
const sale_stored_procedures_service_1 = require("./sale-stored-procedures.service");
const sale_stored_procedures_interface_1 = require("./sale-stored-procedures.interface");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
const prisma_module_1 = require("../../../../../../libs/prisma/prisma.module");
let StoredProceduresModule = class StoredProceduresModule {
};
exports.StoredProceduresModule = StoredProceduresModule;
exports.StoredProceduresModule = StoredProceduresModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        providers: [
            sale_builder_1.SaleBuilder,
            {
                provide: sale_stored_procedures_interface_1.SALE_STORED_PROCEDURES,
                useClass: sale_stored_procedures_service_1.SaleStoredProceduresService
            }
        ],
        exports: [sale_stored_procedures_interface_1.SALE_STORED_PROCEDURES]
    })
], StoredProceduresModule);
//# sourceMappingURL=stored-procedures.module.js.map