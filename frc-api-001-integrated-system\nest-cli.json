{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/api-gateway/tsconfig.app.json"}, "monorepo": true, "root": "apps/api-gateway", "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "auth-service": {"type": "application", "root": "apps/auth-service", "entryFile": "main", "sourceRoot": "apps/auth-service/src", "compilerOptions": {"tsConfigPath": "apps/auth-service/tsconfig.app.json"}}, "commissions-service": {"type": "application", "root": "apps/commissions-service", "entryFile": "main", "sourceRoot": "apps/commissions-service/src", "compilerOptions": {"tsConfigPath": "apps/commissions-service/tsconfig.app.json"}}, "sales-service": {"type": "application", "root": "apps/sales-service", "entryFile": "main", "sourceRoot": "apps/sales-service/src", "compilerOptions": {"tsConfigPath": "apps/sales-service/tsconfig.app.json"}}}}