import { ISaleRepository } from "../../domain/ports/repositories/sale.repository";
import { ICustomerRepository } from "../../domain/ports/repositories/customer.repository";
import { ILocationRepository } from "../../domain/ports/repositories/location.repository";
import { Sale } from "../../domain/models/sale";
import { Customer } from "../../domain/models/customer";
import { Location } from "../../domain/models/location";
export declare const GET_SALE_BY_ID_USE_CASE: unique symbol;
export declare class GetSaleByIdUseCase {
    private readonly _saleRepository;
    private readonly _customerRepository;
    private readonly _locationRepository;
    constructor(_saleRepository: ISaleRepository, _customerRepository: ICustomerRepository, _locationRepository: ILocationRepository);
    execute(saleId: number): Promise<{
        sale: Sale;
        customer: Customer;
        location: Location | null;
    }>;
}
