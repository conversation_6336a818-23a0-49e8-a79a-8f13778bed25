"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaUpdateSaleTransaction = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let PrismaUpdateSaleTransaction = class PrismaUpdateSaleTransaction {
    _prismaService;
    saleBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
    }
    async run(updateSaleDto) {
        return this._prismaService.$transaction(async (prisma) => {
            const existingSale = await prisma.sale.findUnique({
                where: { sale_id: updateSaleDto.saleId },
                include: { Customer: true }
            });
            if (!existingSale) {
                throw new Error(`Venta con ID ${updateSaleDto.saleId} no encontrada`);
            }
            if (updateSaleDto.location && existingSale.Customer.location_id) {
                await prisma.location.update({
                    where: { location_id: existingSale.Customer.location_id },
                    data: {
                        address: updateSaleDto.location.address,
                        department: updateSaleDto.location.department,
                        province: updateSaleDto.location.province,
                        district: updateSaleDto.location.district
                    }
                });
            }
            else if (updateSaleDto.location) {
                const location = await prisma.location.create({
                    data: {
                        address: updateSaleDto.location.address,
                        department: updateSaleDto.location.department,
                        province: updateSaleDto.location.province,
                        district: updateSaleDto.location.district
                    }
                });
                if (updateSaleDto.customer) {
                    updateSaleDto.customer = {
                        ...updateSaleDto.customer,
                        location_id: location.location_id
                    };
                }
                else {
                    await prisma.customer.update({
                        where: { customer_id: existingSale.customer_id },
                        data: { location_id: location.location_id }
                    });
                }
            }
            if (updateSaleDto.customer) {
                await prisma.customer.update({
                    where: { customer_id: existingSale.customer_id },
                    data: {
                        full_name: updateSaleDto.customer.full_name,
                        doi: updateSaleDto.customer.doi,
                        phone_number: updateSaleDto.customer.phone_number,
                        mobile_number: updateSaleDto.customer.mobile_number,
                        document_type_id: updateSaleDto.customer.document_type_id,
                        location_id: updateSaleDto.customer.location_id
                    }
                });
            }
            const updatedSale = await prisma.sale.update({
                where: { sale_id: updateSaleDto.saleId },
                data: {
                    policy_number: updateSaleDto.sale.policy_number || undefined,
                    start_sale_date: updateSaleDto.sale.start_sale_date || undefined,
                    end_sale_date: updateSaleDto.sale.end_sale_date || undefined,
                    net_amount: updateSaleDto.sale.net_amount,
                    total_amount: updateSaleDto.sale.total_amount,
                    condition_sale_id: updateSaleDto.sale.condition_sale_id || undefined,
                    currency_id: updateSaleDto.sale.currency_id || undefined,
                    cia_company_id: updateSaleDto.sale.cia_company_id || undefined,
                    insurance_line_cia_sale_id: updateSaleDto.sale.insurance_line_cia_sale_id || undefined,
                    customer_id: existingSale.customer_id,
                    account_id: updateSaleDto.accountId || existingSale.account_id
                }
            });
            return this.saleBuilder
                .withSaleId(updatedSale.sale_id)
                .withPolicyNumber(updatedSale.policy_number || undefined)
                .withStartSaleDate(updatedSale.start_sale_date || undefined)
                .withEndSaleDate(updatedSale.end_sale_date || undefined)
                .withNetAmount(Number(updatedSale.net_amount) || 0)
                .withTotalAmount(Number(updatedSale.total_amount) || 0)
                .withConditionSaleId(updatedSale.condition_sale_id || undefined)
                .withCurrencyId(updatedSale.currency_id || undefined)
                .withCiaCompanyId(updatedSale.cia_company_id || undefined)
                .withInsuranceLineCiaSaleId(updatedSale.insurance_line_cia_sale_id || undefined)
                .withCustomerId(updatedSale.customer_id)
                .withAccountId(updatedSale.account_id)
                .build();
        });
    }
};
exports.PrismaUpdateSaleTransaction = PrismaUpdateSaleTransaction;
exports.PrismaUpdateSaleTransaction = PrismaUpdateSaleTransaction = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaUpdateSaleTransaction);
//# sourceMappingURL=prisma-update-sale.transaction.js.map