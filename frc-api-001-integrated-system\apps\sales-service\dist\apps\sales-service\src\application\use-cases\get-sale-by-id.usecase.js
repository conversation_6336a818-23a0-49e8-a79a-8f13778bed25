"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSaleByIdUseCase = exports.GET_SALE_BY_ID_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const sale_repository_1 = require("../../domain/ports/repositories/sale.repository");
const customer_repository_1 = require("../../domain/ports/repositories/customer.repository");
const location_repository_1 = require("../../domain/ports/repositories/location.repository");
exports.GET_SALE_BY_ID_USE_CASE = Symbol('GET_SALE_BY_ID_USE_CASE');
let GetSaleByIdUseCase = class GetSaleByIdUseCase {
    _saleRepository;
    _customerRepository;
    _locationRepository;
    constructor(_saleRepository, _customerRepository, _locationRepository) {
        this._saleRepository = _saleRepository;
        this._customerRepository = _customerRepository;
        this._locationRepository = _locationRepository;
    }
    async execute(saleId) {
        const sale = await this._saleRepository.findById(saleId);
        if (!sale) {
            throw new Error(`Venta con ID ${saleId} no encontrada`);
        }
        const customer = await this._customerRepository.findById(sale.customer_id);
        if (!customer) {
            throw new Error(`Cliente con ID ${sale.customer_id} no encontrado`);
        }
        let location = null;
        if (customer.location_id) {
            location = await this._locationRepository.findById(customer.location_id);
        }
        return {
            sale,
            customer,
            location
        };
    }
};
exports.GetSaleByIdUseCase = GetSaleByIdUseCase;
exports.GetSaleByIdUseCase = GetSaleByIdUseCase = __decorate([
    __param(0, (0, common_1.Inject)(sale_repository_1.SALE_REPOSITORY)),
    __param(1, (0, common_1.Inject)(customer_repository_1.CUSTOMER_REPOSITORY)),
    __param(2, (0, common_1.Inject)(location_repository_1.LOCATION_REPOSITORY)),
    __metadata("design:paramtypes", [Object, Object, Object])
], GetSaleByIdUseCase);
//# sourceMappingURL=get-sale-by-id.usecase.js.map