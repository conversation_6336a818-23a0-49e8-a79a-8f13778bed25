import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { IRenewSaleTransaction } from "../../../domain/ports/transactions/renew-sale.transaction";
import { RenewSaleDto } from "../../../domain/ports/transactions/dtos/renew-sale.dto";
import { Sale } from "../../../domain/models/sale";
import { SaleBuilder } from "../../../domain/builders/sale.builder";

@Injectable()
export class PrismaRenewSaleTransaction implements IRenewSaleTransaction {
  private readonly saleBuilder: SaleBuilder;

  constructor(
    private readonly _prismaService: PrismaService,
  ) {
    this.saleBuilder = new SaleBuilder();
  }

  async run(renewSaleDto: RenewSaleDto): Promise<Sale> {
    // Usamos una transacción de Prisma para asegurar atomicidad
    return this._prismaService.$transaction(async (prisma) => {
      // 1. Verificar que la venta original existe
      const originalSale = await prisma.sale.findUnique({
        where: { sale_id: renewSaleDto.originalSaleId }
      });

      if (!originalSale) {
        throw new Error(`Venta original con ID ${renewSaleDto.originalSaleId} no encontrada`);
      }

      // 2. Crear la nueva venta como renovación
      const newSale = await prisma.sale.create({
        data: {
          policy_number: renewSaleDto.newSale.policy_number || originalSale.policy_number || undefined,
          start_sale_date: renewSaleDto.startDate,
          end_sale_date: renewSaleDto.endDate,
          net_amount: renewSaleDto.newSale.net_amount as any || originalSale.net_amount,
          total_amount: renewSaleDto.newSale.total_amount as any || originalSale.total_amount,
          condition_sale_id: renewSaleDto.newSale.condition_sale_id || originalSale.condition_sale_id || undefined,
          currency_id: renewSaleDto.newSale.currency_id || originalSale.currency_id || undefined,
          cia_company_id: renewSaleDto.newSale.cia_company_id || originalSale.cia_company_id || undefined,
          insurance_line_cia_sale_id: renewSaleDto.newSale.insurance_line_cia_sale_id || originalSale.insurance_line_cia_sale_id || undefined,
          customer_id: renewSaleDto.keepCustomerInfo ? originalSale.customer_id : (renewSaleDto.newSale.customer_id || originalSale.customer_id),
          account_id: renewSaleDto.accountId
        }
      });

      // 3. Mapear la entidad Prisma a un modelo de dominio
      return this.saleBuilder
        .withSaleId(newSale.sale_id)
        .withPolicyNumber(newSale.policy_number || undefined)
        .withStartSaleDate(newSale.start_sale_date || undefined)
        .withEndSaleDate(newSale.end_sale_date || undefined)
        .withNetAmount(Number(newSale.net_amount) || 0)
        .withTotalAmount(Number(newSale.total_amount) || 0)
        .withConditionSaleId(newSale.condition_sale_id || undefined)
        .withCurrencyId(newSale.currency_id || undefined)
        .withCiaCompanyId(newSale.cia_company_id || undefined)
        .withInsuranceLineCiaSaleId(newSale.insurance_line_cia_sale_id || undefined)
        .withCustomerId(newSale.customer_id)
        .withAccountId(newSale.account_id)
        .build();
    });
  }
}
