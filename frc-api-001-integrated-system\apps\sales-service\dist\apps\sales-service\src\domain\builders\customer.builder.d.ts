import { Customer } from "../models/customer";
export declare class CustomerBuilder {
    private customer;
    constructor();
    reset(): void;
    build(): Customer;
    withCustomerId(customerId: number): this;
    withFullName(fullName: string): this;
    withDoi(doi: string): this;
    withPhoneNumber(phoneNumber: string): this;
    withMobileNumber(mobileNumber: string): this;
    withDocumentTypeId(documentTypeId: number): this;
    withLocationId(locationId: number): this;
}
