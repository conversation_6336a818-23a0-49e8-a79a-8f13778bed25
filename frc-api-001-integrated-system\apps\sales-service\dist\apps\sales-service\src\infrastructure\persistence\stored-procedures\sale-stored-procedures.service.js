"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleStoredProceduresService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let SaleStoredProceduresService = class SaleStoredProceduresService {
    prismaService;
    saleBuilder;
    constructor(prismaService, saleBuilder) {
        this.prismaService = prismaService;
        this.saleBuilder = saleBuilder;
    }
    async createCompleteSale(saleData, customerData, locationData) {
        return this.prismaService.$transaction(async (prisma) => {
            let customerId;
            if (customerData.doi) {
                const existingCustomer = await prisma.customer.findUnique({
                    where: { doi: customerData.doi }
                });
                if (existingCustomer) {
                    const updatedCustomer = await prisma.customer.update({
                        where: { customer_id: existingCustomer.customer_id },
                        data: {
                            full_name: customerData.full_name,
                            phone_number: customerData.phone_number,
                            mobile_number: customerData.mobile_number,
                            document_type_id: customerData.document_type_id
                        }
                    });
                    customerId = updatedCustomer.customer_id;
                }
                else {
                    customerId = await this.createCustomerWithLocation(prisma, customerData, locationData);
                }
            }
            else {
                customerId = await this.createCustomerWithLocation(prisma, customerData, locationData);
            }
            const sale = await prisma.sale.create({
                data: {
                    policy_number: saleData.policy_number || undefined,
                    start_sale_date: saleData.start_sale_date || undefined,
                    end_sale_date: saleData.end_sale_date || undefined,
                    net_amount: saleData.net_amount,
                    total_amount: saleData.total_amount,
                    condition_sale_id: saleData.condition_sale_id || undefined,
                    currency_id: saleData.currency_id || undefined,
                    cia_company_id: saleData.cia_company_id || undefined,
                    insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id || undefined,
                    customer_id: customerId,
                    account_id: saleData.account_id
                },
                include: {
                    Customer: true
                }
            });
            return this.mapSaleToDomain(sale);
        });
    }
    async updateCompleteSale(saleId, saleData, customerData, locationData) {
        return this.prismaService.$transaction(async (prisma) => {
            const existingSale = await prisma.sale.findUnique({
                where: { sale_id: saleId },
                include: { Customer: true }
            });
            if (!existingSale) {
                throw new Error(`Venta con ID ${saleId} no encontrada`);
            }
            if (customerData) {
                if (existingSale.Customer.location_id && locationData) {
                    await prisma.location.update({
                        where: { location_id: existingSale.Customer.location_id },
                        data: locationData
                    });
                }
                else if (locationData) {
                    const location = await prisma.location.create({
                        data: locationData
                    });
                    await prisma.customer.update({
                        where: { customer_id: existingSale.customer_id },
                        data: {
                            ...customerData,
                            location_id: location.location_id
                        }
                    });
                }
                else {
                    await prisma.customer.update({
                        where: { customer_id: existingSale.customer_id },
                        data: customerData
                    });
                }
            }
            const updatedSale = await prisma.sale.update({
                where: { sale_id: saleId },
                data: {
                    policy_number: saleData.policy_number || undefined,
                    start_sale_date: saleData.start_sale_date || undefined,
                    end_sale_date: saleData.end_sale_date || undefined,
                    net_amount: saleData.net_amount,
                    total_amount: saleData.total_amount,
                    condition_sale_id: saleData.condition_sale_id || undefined,
                    currency_id: saleData.currency_id || undefined,
                    cia_company_id: saleData.cia_company_id || undefined,
                    insurance_line_cia_sale_id: saleData.insurance_line_cia_sale_id || undefined,
                    account_id: saleData.account_id || existingSale.account_id
                },
                include: {
                    Customer: true
                }
            });
            return this.mapSaleToDomain(updatedSale);
        });
    }
    async renewSale(originalSaleId, newSaleData, startDate, endDate, keepCustomerInfo = true, accountId) {
        return this.prismaService.$transaction(async (prisma) => {
            const originalSale = await prisma.sale.findUnique({
                where: { sale_id: originalSaleId },
                include: { Customer: true }
            });
            if (!originalSale) {
                throw new Error(`Venta original con ID ${originalSaleId} no encontrada`);
            }
            const newSale = await prisma.sale.create({
                data: {
                    policy_number: newSaleData.policy_number || originalSale.policy_number || undefined,
                    start_sale_date: startDate,
                    end_sale_date: endDate,
                    net_amount: newSaleData.net_amount || originalSale.net_amount,
                    total_amount: newSaleData.total_amount || originalSale.total_amount,
                    condition_sale_id: newSaleData.condition_sale_id || originalSale.condition_sale_id || undefined,
                    currency_id: newSaleData.currency_id || originalSale.currency_id || undefined,
                    cia_company_id: newSaleData.cia_company_id || originalSale.cia_company_id || undefined,
                    insurance_line_cia_sale_id: newSaleData.insurance_line_cia_sale_id || originalSale.insurance_line_cia_sale_id || undefined,
                    customer_id: originalSale.customer_id,
                    account_id: accountId
                },
                include: {
                    Customer: true
                }
            });
            return this.mapSaleToDomain(newSale);
        });
    }
    async getSalesWithFilters(filters, pagination = { skip: 0, take: 10, orderBy: 'sale_id', orderDirection: 'desc' }) {
        const where = {};
        if (filters.customerId) {
            where.customer_id = filters.customerId;
        }
        if (filters.policyNumber) {
            where.policy_number = { contains: filters.policyNumber };
        }
        if (filters.startDateFrom || filters.startDateTo) {
            where.start_sale_date = {};
            if (filters.startDateFrom) {
                where.start_sale_date.gte = filters.startDateFrom;
            }
            if (filters.startDateTo) {
                where.start_sale_date.lte = filters.startDateTo;
            }
        }
        if (filters.endDateFrom || filters.endDateTo) {
            where.end_sale_date = {};
            if (filters.endDateFrom) {
                where.end_sale_date.gte = filters.endDateFrom;
            }
            if (filters.endDateTo) {
                where.end_sale_date.lte = filters.endDateTo;
            }
        }
        if (filters.accountId) {
            where.account_id = filters.accountId;
        }
        if (filters.conditionSaleId) {
            where.condition_sale_id = filters.conditionSaleId;
        }
        if (filters.currencyId) {
            where.currency_id = filters.currencyId;
        }
        if (filters.ciaCompanyId) {
            where.cia_company_id = filters.ciaCompanyId;
        }
        if (filters.insuranceLineCiaSaleId) {
            where.insurance_line_cia_sale_id = filters.insuranceLineCiaSaleId;
        }
        if (filters.customerDoi || filters.customerName) {
            where.Customer = {};
            if (filters.customerDoi) {
                where.Customer.doi = { contains: filters.customerDoi };
            }
            if (filters.customerName) {
                where.Customer.full_name = { contains: filters.customerName };
            }
        }
        let orderByOption = { id: 'desc' };
        if (pagination.orderBy) {
            orderByOption = {};
            orderByOption[pagination.orderBy] = pagination.orderDirection || 'asc';
        }
        const [sales, total] = await Promise.all([
            this.prismaService.sale.findMany({
                where,
                skip: pagination.skip,
                take: pagination.take,
                orderBy: orderByOption,
                include: {
                    Customer: true
                }
            }),
            this.prismaService.sale.count({ where })
        ]);
        return {
            sales: sales.map(sale => this.mapSaleToDomain(sale)),
            total
        };
    }
    async createCustomerWithLocation(prisma, customerData, locationData) {
        let locationId = undefined;
        if (locationData) {
            const location = await prisma.location.create({
                data: locationData
            });
            locationId = location.location_id;
        }
        const customer = await prisma.customer.create({
            data: {
                full_name: customerData.full_name,
                doi: customerData.doi,
                phone_number: customerData.phone_number,
                mobile_number: customerData.mobile_number,
                document_type_id: customerData.document_type_id,
                location_id: locationId
            }
        });
        return customer.customer_id;
    }
    mapSaleToDomain(sale) {
        return this.saleBuilder
            .withSaleId(sale.sale_id)
            .withPolicyNumber(sale.policy_number || undefined)
            .withStartSaleDate(sale.start_sale_date || undefined)
            .withEndSaleDate(sale.end_sale_date || undefined)
            .withNetAmount(Number(sale.net_amount) || 0)
            .withTotalAmount(Number(sale.total_amount) || 0)
            .withConditionSaleId(sale.condition_sale_id || undefined)
            .withCurrencyId(sale.currency_id || undefined)
            .withCiaCompanyId(sale.cia_company_id || undefined)
            .withInsuranceLineCiaSaleId(sale.insurance_line_cia_sale_id || undefined)
            .withCustomerId(sale.customer_id)
            .withAccountId(sale.account_id)
            .build();
    }
};
exports.SaleStoredProceduresService = SaleStoredProceduresService;
exports.SaleStoredProceduresService = SaleStoredProceduresService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService,
        sale_builder_1.SaleBuilder])
], SaleStoredProceduresService);
//# sourceMappingURL=sale-stored-procedures.service.js.map