"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const auth_service_1 = require("../../services/auth/auth.service");
const create_sale_usecase_1 = require("../../../application/use-cases/create-sale.usecase");
const update_sale_usecase_1 = require("../../../application/use-cases/update-sale.usecase");
const get_sale_by_id_usecase_1 = require("../../../application/use-cases/get-sale-by-id.usecase");
const get_sales_by_page_usecase_1 = require("../../../application/use-cases/get-sales-by-page.usecase");
const renew_sale_usecase_1 = require("../../../application/use-cases/renew-sale.usecase");
const delete_sale_usecase_1 = require("../../../application/use-cases/delete-sale.usecase");
const create_sale_request_dto_1 = require("./dtos/create-sale.request.dto");
const update_sale_request_dto_1 = require("./dtos/update-sale.request.dto");
const filter_sales_request_dto_1 = require("./dtos/filter-sales.request.dto");
const api_response_presenter_1 = require("./presenters/api-response.presenter");
const paginated_response_presenter_1 = require("./presenters/paginated-response.presenter");
let SaleController = class SaleController {
    createSaleUseCase;
    updateSaleUseCase;
    getSaleByIdUseCase;
    getSalesByPageUseCase;
    renewSaleUseCase;
    deleteSaleUseCase;
    authService;
    constructor(createSaleUseCase, updateSaleUseCase, getSaleByIdUseCase, getSalesByPageUseCase, renewSaleUseCase, deleteSaleUseCase, authService) {
        this.createSaleUseCase = createSaleUseCase;
        this.updateSaleUseCase = updateSaleUseCase;
        this.getSaleByIdUseCase = getSaleByIdUseCase;
        this.getSalesByPageUseCase = getSalesByPageUseCase;
        this.renewSaleUseCase = renewSaleUseCase;
        this.deleteSaleUseCase = deleteSaleUseCase;
        this.authService = authService;
    }
    async createSale(createSaleRequestDto, request) {
        try {
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            const saleData = {
                policyNumber: createSaleRequestDto.sale.policy_number,
                startSaleDate: createSaleRequestDto.sale.start_sale_date,
                endSaleDate: createSaleRequestDto.sale.end_sale_date,
                netAmount: createSaleRequestDto.sale.net_amount,
                totalAmount: createSaleRequestDto.sale.total_amount,
                conditionSaleId: createSaleRequestDto.sale.condition_sale_id,
                currencyId: createSaleRequestDto.sale.currency_id,
                ciaCompanyId: createSaleRequestDto.sale.cia_company_id,
                insuranceLineCiaSaleId: createSaleRequestDto.sale.insurance_line_cia_sale_id,
                customer: {
                    fullName: createSaleRequestDto.customer.full_name,
                    doi: createSaleRequestDto.customer.doi,
                    phoneNumber: createSaleRequestDto.customer.phone_number,
                    mobileNumber: createSaleRequestDto.customer.mobile_number,
                    documentTypeId: createSaleRequestDto.customer.document_type_id
                },
                location: createSaleRequestDto.location ? {
                    address: createSaleRequestDto.location.address,
                    department: createSaleRequestDto.location.department,
                    province: createSaleRequestDto.location.province,
                    district: createSaleRequestDto.location.district
                } : undefined
            };
            const createdSale = await this.createSaleUseCase.execute(saleData, token);
            return api_response_presenter_1.ApiResponsePresenter.success(common_1.HttpStatus.CREATED.toString(), createdSale, common_1.HttpStatus.CREATED);
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.UNAUTHORIZED.toString(), undefined, common_1.HttpStatus.UNAUTHORIZED);
            }
            return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString(), undefined, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateSale(id, updateSaleRequestDto, request) {
        try {
            if (id !== updateSaleRequestDto.saleId) {
                return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.BAD_REQUEST.toString(), undefined, common_1.HttpStatus.BAD_REQUEST);
            }
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            const saleData = {
                policyNumber: updateSaleRequestDto.sale.policy_number,
                startSaleDate: updateSaleRequestDto.sale.start_sale_date,
                endSaleDate: updateSaleRequestDto.sale.end_sale_date,
                netAmount: updateSaleRequestDto.sale.net_amount,
                totalAmount: updateSaleRequestDto.sale.total_amount,
                conditionSaleId: updateSaleRequestDto.sale.condition_sale_id,
                currencyId: updateSaleRequestDto.sale.currency_id,
                ciaCompanyId: updateSaleRequestDto.sale.cia_company_id,
                insuranceLineCiaSaleId: updateSaleRequestDto.sale.insurance_line_cia_sale_id,
                customer: {
                    fullName: updateSaleRequestDto.customer.full_name,
                    doi: updateSaleRequestDto.customer.doi,
                    phoneNumber: updateSaleRequestDto.customer.phone_number,
                    mobileNumber: updateSaleRequestDto.customer.mobile_number,
                    documentTypeId: updateSaleRequestDto.customer.document_type_id
                },
                location: updateSaleRequestDto.location ? {
                    address: updateSaleRequestDto.location.address,
                    department: updateSaleRequestDto.location.department,
                    province: updateSaleRequestDto.location.province,
                    district: updateSaleRequestDto.location.district
                } : undefined
            };
            const updatedSale = await this.updateSaleUseCase.execute(id, saleData, token);
            return api_response_presenter_1.ApiResponsePresenter.success(common_1.HttpStatus.OK.toString(), updatedSale);
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.UNAUTHORIZED.toString(), undefined, common_1.HttpStatus.UNAUTHORIZED);
            }
            return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString(), undefined, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async renewSale(id, request) {
        try {
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            const renewedSale = await this.renewSaleUseCase.execute(id, {}, token);
            return api_response_presenter_1.ApiResponsePresenter.success(common_1.HttpStatus.OK.toString(), renewedSale);
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.UNAUTHORIZED.toString(), undefined, common_1.HttpStatus.UNAUTHORIZED);
            }
            return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString(), undefined, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSaleById(id, request) {
        try {
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            const saleData = await this.getSaleByIdUseCase.execute(id);
            if (!saleData) {
                return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.NOT_FOUND.toString(), undefined, common_1.HttpStatus.NOT_FOUND);
            }
            return api_response_presenter_1.ApiResponsePresenter.success(common_1.HttpStatus.OK.toString(), saleData.sale);
        }
        catch (error) {
            return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString(), undefined, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSalesWithFilters(filterDto, request) {
        try {
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            const { page = 1, limit = 10, ...filters } = filterDto;
            const result = await this.getSalesByPageUseCase.execute(page);
            return paginated_response_presenter_1.PaginatedResponsePresenter.fromPagination(result.sales, result.totalSales, page, limit, common_1.HttpStatus.OK.toString());
        }
        catch (error) {
            throw new Error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString());
        }
    }
    async deleteSale(id, request) {
        try {
            const token = this.authService.extractTokenFromHeader(request.headers.authorization || '');
            this.authService.verifyToken(token);
            await this.deleteSaleUseCase.execute(id, false, token);
            return api_response_presenter_1.ApiResponsePresenter.success(common_1.HttpStatus.NO_CONTENT.toString(), undefined, common_1.HttpStatus.NO_CONTENT);
        }
        catch (error) {
            return api_response_presenter_1.ApiResponsePresenter.error(common_1.HttpStatus.INTERNAL_SERVER_ERROR.toString(), undefined, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.SaleController = SaleController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_sale_request_dto_1.CreateSaleRequestDto, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "createSale", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_sale_request_dto_1.UpdateSaleRequestDto, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "updateSale", null);
__decorate([
    (0, common_1.Post)(':id/renew'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "renewSale", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "getSaleById", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_sales_request_dto_1.FilterSalesRequestDto, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "getSalesWithFilters", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SaleController.prototype, "deleteSale", null);
exports.SaleController = SaleController = __decorate([
    (0, common_1.Controller)('sales'),
    __param(0, (0, common_1.Inject)(create_sale_usecase_1.CREATE_SALE_USE_CASE)),
    __param(1, (0, common_1.Inject)(update_sale_usecase_1.UPDATE_SALE_USE_CASE)),
    __param(2, (0, common_1.Inject)(get_sale_by_id_usecase_1.GET_SALE_BY_ID_USE_CASE)),
    __param(3, (0, common_1.Inject)(get_sales_by_page_usecase_1.GET_SALES_BY_PAGE_USE_CASE)),
    __param(4, (0, common_1.Inject)(renew_sale_usecase_1.RENEW_SALE_USE_CASE)),
    __param(5, (0, common_1.Inject)(delete_sale_usecase_1.DELETE_SALE_USE_CASE)),
    __metadata("design:paramtypes", [create_sale_usecase_1.CreateSaleUseCase,
        update_sale_usecase_1.UpdateSaleUseCase,
        get_sale_by_id_usecase_1.GetSaleByIdUseCase,
        get_sales_by_page_usecase_1.GetSalesByPageUseCase,
        renew_sale_usecase_1.RenewSaleUseCase,
        delete_sale_usecase_1.DeleteSaleUseCase,
        auth_service_1.AuthService])
], SaleController);
//# sourceMappingURL=sale.controller.js.map