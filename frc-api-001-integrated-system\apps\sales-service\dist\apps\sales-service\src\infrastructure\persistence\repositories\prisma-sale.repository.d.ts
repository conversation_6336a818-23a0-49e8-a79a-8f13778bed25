import { PrismaService } from "../../../../../../libs/prisma/prisma.service";
import { ISaleRepository } from "../../../domain/ports/repositories/sale.repository";
import { Sale } from "../../../domain/models/sale";
export declare class PrismaSaleRepository implements ISaleRepository {
    private readonly _prismaService;
    private readonly saleBuilder;
    constructor(_prismaService: PrismaService);
    findAll(): Promise<Sale[]>;
    findById(id: number): Promise<Sale | null>;
    create(sale: Sale): Promise<Sale>;
    update(id: number, sale: Sale): Promise<Sale>;
    delete(id: number): Promise<void>;
    private mapPrismaToSale;
}
