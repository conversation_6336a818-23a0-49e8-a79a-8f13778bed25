"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaDeleteSaleTransaction = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let PrismaDeleteSaleTransaction = class PrismaDeleteSaleTransaction {
    _prismaService;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
    }
    async run(deleteSaleDto) {
        await this._prismaService.$transaction(async (prisma) => {
            const existingSale = await prisma.sale.findUnique({
                where: { sale_id: deleteSaleDto.saleId }
            });
            if (!existingSale) {
                throw new Error(`Venta con ID ${deleteSaleDto.saleId} no encontrada`);
            }
            await prisma.sale.delete({
                where: { sale_id: deleteSaleDto.saleId }
            });
        });
    }
};
exports.PrismaDeleteSaleTransaction = PrismaDeleteSaleTransaction;
exports.PrismaDeleteSaleTransaction = PrismaDeleteSaleTransaction = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaDeleteSaleTransaction);
//# sourceMappingURL=prisma-delete-sale.transaction.js.map