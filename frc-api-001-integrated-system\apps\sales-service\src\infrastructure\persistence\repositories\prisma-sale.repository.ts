import { Injectable } from "@nestjs/common";
import { SalesServicePrismaService } from "../prisma.service";
import { SALE_REPOSITORY, ISaleRepository } from "../../../domain/ports/repositories/sale.repository";
import { Sale } from "../../../domain/models/sale";
import { SaleBuilder } from "../../../domain/builders/sale.builder";

@Injectable()
export class PrismaSaleRepository implements ISaleRepository {
  private readonly saleBuilder: SaleBuilder;

  constructor(
    private readonly _prismaService: SalesServicePrismaService,
  ) {
    this.saleBuilder = new SaleBuilder();
  }

  async findAll(): Promise<Sale[]> {
    const sales = await this._prismaService.sale.findMany();

    return sales.map(sale => this.mapPrismaToSale(sale));
  }

  async findById(id: number): Promise<Sale | null> {
    const sale = await this._prismaService.sale.findUnique({
      where: { sale_id: id }
    });

    if (!sale) return null;

    return this.mapPrismaToSale(sale);
  }

  async create(sale: Sale): Promise<Sale> {
    const createdSale = await this._prismaService.sale.create({
      data: {
        policy_number: sale.policy_number,
        start_sale_date: sale.start_sale_date,
        end_sale_date: sale.end_sale_date,
        net_amount: sale.net_amount,
        total_amount: sale.total_amount,
        condition_sale_id: sale.condition_sale_id,
        currency_id: sale.currency_id,
        cia_company_id: sale.cia_company_id,
        insurance_line_cia_sale_id: sale.insurance_line_cia_sale_id,
        customer_id: sale.customer_id,
        account_id: sale.account_id
      }
    });

    return this.mapPrismaToSale(createdSale);
  }

  async update(id: number, sale: Sale): Promise<Sale> {
    const updatedSale = await this._prismaService.sale.update({
      where: { sale_id: id },
      data: {
        policy_number: sale.policy_number,
        start_sale_date: sale.start_sale_date,
        end_sale_date: sale.end_sale_date,
        net_amount: sale.net_amount,
        total_amount: sale.total_amount,
        condition_sale_id: sale.condition_sale_id,
        currency_id: sale.currency_id,
        cia_company_id: sale.cia_company_id,
        insurance_line_cia_sale_id: sale.insurance_line_cia_sale_id,
        customer_id: sale.customer_id,
        account_id: sale.account_id
      }
    });

    return this.mapPrismaToSale(updatedSale);
  }

  async delete(id: number): Promise<void> {
    // Eliminación física ya que no hay campo is_deleted
    await this._prismaService.sale.delete({
      where: { sale_id: id }
    });
  }

  // Método auxiliar para mapear entidades de Prisma a modelos de dominio
  private mapPrismaToSale(prismaEntity: any): Sale {
    return this.saleBuilder
      .withSaleId(prismaEntity.sale_id)
      .withPolicyNumber(prismaEntity.policy_number)
      .withStartSaleDate(prismaEntity.start_sale_date)
      .withEndSaleDate(prismaEntity.end_sale_date)
      .withNetAmount(prismaEntity.net_amount)
      .withTotalAmount(prismaEntity.total_amount)
      .withConditionSaleId(prismaEntity.condition_sale_id)
      .withCurrencyId(prismaEntity.currency_id)
      .withCiaCompanyId(prismaEntity.cia_company_id)
      .withInsuranceLineCiaSaleId(prismaEntity.insurance_line_cia_sale_id)
      .withCustomerId(prismaEntity.customer_id)
      .withAccountId(prismaEntity.account_id)
      .build();
  }
}
