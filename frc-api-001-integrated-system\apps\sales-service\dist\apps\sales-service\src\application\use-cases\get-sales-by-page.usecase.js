"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSalesByPageUseCase = exports.GET_SALES_BY_PAGE_USE_CASE = void 0;
const common_1 = require("@nestjs/common");
const sale_repository_1 = require("../../domain/ports/repositories/sale.repository");
const catalog_repository_1 = require("../../domain/ports/repositories/catalog.repository");
const customer_repository_1 = require("../../domain/ports/repositories/customer.repository");
exports.GET_SALES_BY_PAGE_USE_CASE = Symbol('GET_SALES_BY_PAGE_USE_CASE');
let GetSalesByPageUseCase = class GetSalesByPageUseCase {
    _saleRepository;
    _catalogRepository;
    _customerRepository;
    constructor(_saleRepository, _catalogRepository, _customerRepository) {
        this._saleRepository = _saleRepository;
        this._catalogRepository = _catalogRepository;
        this._customerRepository = _customerRepository;
    }
    async execute(page) {
        const pageSize = 15;
        const sales = await this._saleRepository.findAll();
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedSales = sales.slice(startIndex, endIndex);
        const totalSales = sales.length;
        const totalPages = Math.ceil(totalSales / pageSize);
        const saleListItems = await this.transformSalesToListItems(paginatedSales);
        return {
            sales: paginatedSales,
            totalSales,
            page,
            totalPages
        };
    }
    async transformSalesToListItems(sales) {
        const [ciaCompanies, insuranceLines, documentTypes, currencies] = await Promise.all([
            this._catalogRepository.getAllCiaCompanies(),
            this._catalogRepository.getAllInsuranceLineCiaSales(),
            this._catalogRepository.getAllDocumentTypes(),
            this._catalogRepository.getAllCurrencies()
        ]);
        const ciaCompanyMap = new Map(ciaCompanies.map(c => [c.cia_company_id, c.company_name]));
        const insuranceLineMap = new Map(insuranceLines.map(il => [il.insurance_line_cia_sale_id, il.insurance_name]));
        const documentTypeMap = new Map(documentTypes.map(dt => [dt.document_type_id, dt.document_name]));
        const currencyMap = new Map(currencies.map(c => [c.currency_id, c.currency_type]));
        return Promise.all(sales.map(async (sale) => {
            const customer = await this._customerRepository.findById(sale.customer_id);
            const saleItem = {
                saleId: sale.sale_id ?? 0,
                policyNumber: sale.policy_number ?? '',
                insurerName: ciaCompanyMap.get(sale.cia_company_id) || 'No especificado',
                insuranceLine: insuranceLineMap.get(sale.insurance_line_cia_sale_id) || 'No especificado',
                saleDate: sale.start_sale_date ?? new Date(),
                documentType: customer ? documentTypeMap.get(customer.document_type_id) || 'No especificado' : 'No especificado',
                documentNumber: customer ? customer.doi || 'No especificado' : 'No especificado',
                customerName: customer ? customer.full_name : 'No especificado',
                currency: currencyMap.get(sale.currency_id) || 'No especificado',
                netAmount: sale.net_amount ?? 0,
                totalAmount: sale.total_amount ?? 0
            };
            return saleItem;
        }));
    }
};
exports.GetSalesByPageUseCase = GetSalesByPageUseCase;
exports.GetSalesByPageUseCase = GetSalesByPageUseCase = __decorate([
    __param(0, (0, common_1.Inject)(sale_repository_1.SALE_REPOSITORY)),
    __param(1, (0, common_1.Inject)(catalog_repository_1.CATALOG_REPOSITORY)),
    __param(2, (0, common_1.Inject)(customer_repository_1.CUSTOMER_REPOSITORY)),
    __metadata("design:paramtypes", [Object, Object, Object])
], GetSalesByPageUseCase);
//# sourceMappingURL=get-sales-by-page.usecase.js.map