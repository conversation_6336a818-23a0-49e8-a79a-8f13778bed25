generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  account_id Int          @id @default(autoincrement())
  nickname   String       @unique(map: "Uq_Account_Nickname") @db.VarChar(50)
  password   String       @db.VarChar(255)
  is_active  Boolean?     @default(true)
  user_id    Int
  role_id    Int
  office_id  Int?
  Office     Office?      @relation(fields: [office_id], references: [office_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Account_OfficeId")
  Role       Role         @relation(fields: [role_id], references: [role_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Account_RoleId")
  User       User         @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Account_UserId")
  Invoice    Invoice[]
  Permission Permission[]
  Sale       Sale[]

  @@index([office_id], map: "Fk_Account_OfficeId")
  @@index([role_id], map: "Fk_Account_RoleId")
  @@index([user_id], map: "Fk_Account_UserId")
}

model Application {
  application_id   Int          @id @default(autoincrement())
  application_name String       @unique(map: "Uq_Application_ApplicationName") @db.VarChar(100)
  Permission       Permission[]
}

model CiaCompany {
  cia_company_id Int       @id @default(autoincrement())
  company_name   String    @unique(map: "Uq_CiaCompany_CompanyName") @db.VarChar(100)
  Invoice        Invoice[]
  Sale           Sale[]
}

model ConditionSale {
  condition_sale_id Int    @id @default(autoincrement())
  condition_name    String @unique(map: "Uq_ConditionSale_ConditionName") @db.VarChar(50)
  Sale              Sale[]
}

model Currency {
  currency_id   Int       @id @default(autoincrement())
  currency_type String    @unique(map: "Uq_Currency_CurrencyType") @db.VarChar(10)
  Invoice       Invoice[]
  Sale          Sale[]
}

model Customer {
  customer_id      Int          @id @default(autoincrement())
  full_name        String       @db.VarChar(100)
  doi              String?      @unique(map: "Uq_Customer_Doi") @db.VarChar(11)
  phone_number     String?      @db.VarChar(15)
  mobile_number    String?      @db.VarChar(15)
  document_type_id Int
  location_id      Int?
  DocumentType     DocumentType @relation(fields: [document_type_id], references: [document_type_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Customer_DocumentTypeId")
  Location         Location?    @relation(fields: [location_id], references: [location_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Customer_LocationId")
  Invoice          Invoice[]
  Sale             Sale[]

  @@index([document_type_id], map: "Fk_Customer_DocumentTypeId")
  @@index([location_id], map: "Fk_Customer_LocationId")
}

model DocumentType {
  document_type_id Int        @id @default(autoincrement())
  document_name    String     @unique(map: "Uq_DocumentType_DocumentName") @db.VarChar(50)
  Customer         Customer[]
}

model InsuranceLineCia {
  insurance_line_cia_id Int       @id @default(autoincrement())
  insurance_name        String    @unique(map: "Uq_InsuranceLineCia_Name") @db.VarChar(100)
  Invoice               Invoice[]
}

model InsuranceLineCiaSale {
  insurance_line_cia_sale_id Int    @id @default(autoincrement())
  insurance_name             String @unique(map: "Uq_InsuranceLineCiaSale_Name") @db.VarChar(100)
  Sale                       Sale[]
}

model InsuranceLineCode {
  insurance_line_code_id Int       @id @default(autoincrement())
  insurance_name         String    @unique(map: "Uq_InsuranceLineCode_Name") @db.VarChar(100)
  Invoice                Invoice[]
}

model InsuranceLineSbs {
  insurance_line_sbs_id Int       @id @default(autoincrement())
  insurance_name        String    @unique(map: "Uq_InsuranceLineSbs_Name") @db.VarChar(100)
  Invoice               Invoice[]
}

model Invoice {
  invoice_id             Int                @id @default(autoincrement())
  invoice_date           DateTime?          @db.Date
  invoice_number         String?            @db.VarChar(50)
  policy_number          String?            @db.VarChar(100)
  receipt_number         String?            @db.VarChar(50)
  liquidation            Decimal?           @db.Decimal(12, 2)
  net_amount             Decimal?           @db.Decimal(12, 2)
  commission_amount      Decimal?           @db.Decimal(12, 2)
  currency_id            Int?
  cia_company_id         Int?
  insurance_line_sbs_id  Int?
  insurance_line_cia_id  Int?
  insurance_line_code_id Int?
  customer_id            Int
  account_id             Int?
  Account                Account?           @relation(fields: [account_id], references: [account_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_AccountId")
  CiaCompany             CiaCompany?        @relation(fields: [cia_company_id], references: [cia_company_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_CiaCompanyId")
  Currency               Currency?          @relation(fields: [currency_id], references: [currency_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_CurrencyId")
  Customer               Customer           @relation(fields: [customer_id], references: [customer_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_CustomerId")
  InsuranceLineCia       InsuranceLineCia?  @relation(fields: [insurance_line_cia_id], references: [insurance_line_cia_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_InsuranceLineCiaId")
  InsuranceLineCode      InsuranceLineCode? @relation(fields: [insurance_line_code_id], references: [insurance_line_code_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_InsuranceLineCodeId")
  InsuranceLineSbs       InsuranceLineSbs?  @relation(fields: [insurance_line_sbs_id], references: [insurance_line_sbs_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Invoice_InsuranceLineSbsId")

  @@index([account_id], map: "Fk_Invoice_AccountId")
  @@index([cia_company_id], map: "Fk_Invoice_CiaCompanyId")
  @@index([currency_id], map: "Fk_Invoice_CurrencyId")
  @@index([customer_id], map: "Fk_Invoice_CustomerId")
  @@index([insurance_line_cia_id], map: "Fk_Invoice_InsuranceLineCiaId")
  @@index([insurance_line_code_id], map: "Fk_Invoice_InsuranceLineCodeId")
  @@index([insurance_line_sbs_id], map: "Fk_Invoice_InsuranceLineSbsId")
}

model Location {
  location_id Int        @id @default(autoincrement())
  address     String?    @db.VarChar(200)
  department  String?    @db.VarChar(50)
  province    String?    @db.VarChar(50)
  district    String?    @db.VarChar(50)
  Customer    Customer[]
}

model Office {
  office_id       Int       @id @default(autoincrement())
  office_location String    @db.VarChar(100)
  Account         Account[]
}

model Permission {
  application_id Int
  account_id     Int
  committed_at   DateTime?   @default(now()) @db.DateTime(0)
  Account        Account     @relation(fields: [account_id], references: [account_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Permission_AccountId")
  Application    Application @relation(fields: [application_id], references: [application_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Permission_ApplicationId")

  @@id([application_id, account_id])
  @@index([account_id], map: "Fk_Permission_AccountId")
}

model Role {
  role_id   Int       @id @default(autoincrement())
  role_name String    @unique(map: "Uq_Role_RoleName") @db.VarChar(50)
  Account   Account[]
}

model Sale {
  sale_id                    Int                   @id @default(autoincrement())
  policy_number              String?               @db.VarChar(100)
  start_sale_date            DateTime?             @db.Date
  end_sale_date              DateTime?             @db.Date
  net_amount                 Decimal?              @db.Decimal(12, 2)
  total_amount               Decimal?              @db.Decimal(12, 2)
  customer_id                Int
  account_id                 Int
  condition_sale_id          Int?
  currency_id                Int?
  cia_company_id             Int?
  insurance_line_cia_sale_id Int?
  Account                    Account               @relation(fields: [account_id], references: [account_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_AccountId")
  CiaCompany                 CiaCompany?           @relation(fields: [cia_company_id], references: [cia_company_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_CiaCompanyId")
  ConditionSale              ConditionSale?        @relation(fields: [condition_sale_id], references: [condition_sale_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_ConditionSaleId")
  Currency                   Currency?             @relation(fields: [currency_id], references: [currency_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_CurrencyId")
  Customer                   Customer              @relation(fields: [customer_id], references: [customer_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_CustomerId")
  InsuranceLineCiaSale       InsuranceLineCiaSale? @relation(fields: [insurance_line_cia_sale_id], references: [insurance_line_cia_sale_id], onDelete: NoAction, onUpdate: NoAction, map: "Fk_Sale_InsuranceLineCiaSaleId")

  @@index([account_id], map: "Fk_Sale_AccountId")
  @@index([cia_company_id], map: "Fk_Sale_CiaCompanyId")
  @@index([condition_sale_id], map: "Fk_Sale_ConditionSaleId")
  @@index([currency_id], map: "Fk_Sale_CurrencyId")
  @@index([customer_id], map: "Fk_Sale_CustomerId")
  @@index([insurance_line_cia_sale_id], map: "Fk_Sale_InsuranceLineCiaSaleId")
}

model User {
  user_id       Int       @id @default(autoincrement())
  names         String    @db.VarChar(50)
  lastnames     String    @db.VarChar(50)
  doi           String    @unique(map: "Uq_User_Doi") @db.VarChar(11)
  email         String    @unique(map: "Uq_User_Email") @db.VarChar(100)
  mobile_number String    @db.VarChar(15)
  Account       Account[]
}
