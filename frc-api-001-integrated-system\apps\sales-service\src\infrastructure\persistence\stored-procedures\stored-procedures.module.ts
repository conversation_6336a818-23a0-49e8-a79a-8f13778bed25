import { Modu<PERSON> } from '@nestjs/common';
import { SaleStoredProceduresService } from './sale-stored-procedures.service';
import { SALE_STORED_PROCEDURES } from './sale-stored-procedures.interface';
import { SaleBuilder } from '../../../domain/builders/sale.builder';
import { PrismaModule } from '../../../../../../libs/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [
    SaleBuilder,
    {
      provide: SALE_STORED_PROCEDURES,
      useClass: SaleStoredProceduresService
    }
  ],
  exports: [SALE_STORED_PROCEDURES]
})
export class StoredProceduresModule {}
