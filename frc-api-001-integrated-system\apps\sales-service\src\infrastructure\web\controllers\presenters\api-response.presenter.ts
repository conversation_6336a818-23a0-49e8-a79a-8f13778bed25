import { HttpStatus } from '@nestjs/common';

/**
 * Clase base para presentadores de respuestas API
 */
export class ApiResponsePresenter<T> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: string;
  statusCode: number;

  constructor(success: boolean, message: string, data?: T, statusCode: number = HttpStatus.OK) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.statusCode = statusCode;
  }

  /**
   * Crea una respuesta exitosa
   * @param message Mensaje de éxito
   * @param data Datos opcionales
   * @param statusCode Código de estado HTTP (por defecto 200 OK)
   */
  static success<T>(message: string, data?: T, statusCode: number = HttpStatus.OK): ApiResponsePresenter<T> {
    return new ApiResponsePresenter<T>(true, message, data, statusCode);
  }

  /**
   * Crea una respuesta de error
   * @param message Mensaje de error
   * @param data Datos opcionales
   * @param statusCode Código de estado HTTP (por defecto 400 BAD_REQUEST)
   */
  static error<T>(message: string, data?: T, statusCode: number = HttpStatus.BAD_REQUEST): ApiResponsePresenter<T> {
    return new ApiResponsePresenter<T>(false, message, data, statusCode);
  }
}
