import { Customer } from "../models/customer";

export class CustomerBuilder {
  private customer: Customer;

  constructor() {
    this.reset();
  }

  reset(): void {
    this.customer = new Customer();
  }

  build(): Customer {
    const builtCustomer = this.customer;
    this.reset();
    return builtCustomer;
  }

  withCustomerId(customerId: number): this {
    this.customer.customer_id = customerId;
    return this;
  }

  withFullName(fullName: string): this {
    this.customer.full_name = fullName;
    return this;
  }

  withDoi(doi: string): this {
    this.customer.doi = doi;
    return this;
  }

  withPhoneNumber(phoneNumber: string): this {
    this.customer.phone_number = phoneNumber;
    return this;
  }

  withMobileNumber(mobileNumber: string): this {
    this.customer.mobile_number = mobileNumber;
    return this;
  }

  withDocumentTypeId(documentTypeId: number): this {
    this.customer.document_type_id = documentTypeId;
    return this;
  }

  withLocationId(locationId: number): this {
    this.customer.location_id = locationId;
    return this;
  }
}
