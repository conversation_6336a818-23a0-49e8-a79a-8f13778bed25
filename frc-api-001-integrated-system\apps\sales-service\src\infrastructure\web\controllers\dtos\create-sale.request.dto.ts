import { IsString, IsN<PERSON>ber, IsOptional, IsDate, IsObject, ValidateNested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO para la información del cliente en una solicitud de creación de venta
 */
export class CreateCustomerDto {
  @IsString()
  @IsNotEmpty()
  full_name: string;

  @IsString()
  @IsNotEmpty()
  doi: string;

  @IsString()
  @IsOptional()
  phone_number?: string;

  @IsString()
  @IsOptional()
  mobile_number?: string;

  @IsNumber()
  @IsNotEmpty()
  document_type_id: number;
}

/**
 * DTO para la información de ubicación en una solicitud de creación de venta
 */
export class CreateLocationDto {
  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  department: string;

  @IsString()
  @IsNotEmpty()
  province: string;

  @IsString()
  @IsNotEmpty()
  district: string;
}

/**
 * DTO para la información de venta en una solicitud de creación
 */
export class CreateSaleInfoDto {
  @IsString()
  @IsOptional()
  policy_number?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  start_sale_date?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  end_sale_date?: Date;

  @IsNumber()
  @IsOptional()
  net_amount?: number;

  @IsNumber()
  @IsOptional()
  total_amount?: number;

  @IsNumber()
  @IsOptional()
  condition_sale_id?: number;

  @IsNumber()
  @IsOptional()
  currency_id?: number;

  @IsNumber()
  @IsOptional()
  cia_company_id?: number;

  @IsNumber()
  @IsOptional()
  insurance_line_cia_sale_id?: number;
}

/**
 * DTO para la solicitud de creación de una venta completa
 */
export class CreateSaleRequestDto {
  @IsObject()
  @ValidateNested()
  @Type(() => CreateSaleInfoDto)
  @IsNotEmpty()
  sale: CreateSaleInfoDto;

  @IsObject()
  @ValidateNested()
  @Type(() => CreateCustomerDto)
  @IsNotEmpty()
  customer: CreateCustomerDto;

  @IsObject()
  @ValidateNested()
  @Type(() => CreateLocationDto)
  @IsOptional()
  location?: CreateLocationDto;
}
