"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleBuilder = void 0;
const sale_1 = require("../models/sale");
class SaleBuilder {
    sale;
    constructor() {
        this.reset();
    }
    reset() {
        this.sale = new sale_1.Sale();
    }
    build() {
        const builtSale = this.sale;
        this.reset();
        return builtSale;
    }
    withSaleId(saleId) {
        this.sale.sale_id = saleId;
        return this;
    }
    withPolicyNumber(policyNumber) {
        this.sale.policy_number = policyNumber;
        return this;
    }
    withStartSaleDate(startSaleDate) {
        this.sale.start_sale_date = startSaleDate;
        return this;
    }
    withEndSaleDate(endSaleDate) {
        this.sale.end_sale_date = endSaleDate;
        return this;
    }
    withNetAmount(netAmount) {
        this.sale.net_amount = netAmount;
        return this;
    }
    withTotalAmount(totalAmount) {
        this.sale.total_amount = totalAmount;
        return this;
    }
    withCustomerId(customerId) {
        this.sale.customer_id = customerId;
        return this;
    }
    withAccountId(accountId) {
        this.sale.account_id = accountId;
        return this;
    }
    withConditionSaleId(conditionSaleId) {
        this.sale.condition_sale_id = conditionSaleId;
        return this;
    }
    withCurrencyId(currencyId) {
        this.sale.currency_id = currencyId;
        return this;
    }
    withCiaCompanyId(ciaCompanyId) {
        this.sale.cia_company_id = ciaCompanyId;
        return this;
    }
    withInsuranceLineCiaSaleId(insuranceLineCiaSaleId) {
        this.sale.insurance_line_cia_sale_id = insuranceLineCiaSaleId;
        return this;
    }
}
exports.SaleBuilder = SaleBuilder;
//# sourceMappingURL=sale.builder.js.map