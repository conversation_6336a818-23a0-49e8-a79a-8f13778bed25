"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaRenewSaleTransaction = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const sale_builder_1 = require("../../../domain/builders/sale.builder");
let PrismaRenewSaleTransaction = class PrismaRenewSaleTransaction {
    _prismaService;
    saleBuilder;
    constructor(_prismaService) {
        this._prismaService = _prismaService;
        this.saleBuilder = new sale_builder_1.SaleBuilder();
    }
    async run(renewSaleDto) {
        return this._prismaService.$transaction(async (prisma) => {
            const originalSale = await prisma.sale.findUnique({
                where: { sale_id: renewSaleDto.originalSaleId }
            });
            if (!originalSale) {
                throw new Error(`Venta original con ID ${renewSaleDto.originalSaleId} no encontrada`);
            }
            const newSale = await prisma.sale.create({
                data: {
                    policy_number: renewSaleDto.newSale.policy_number || originalSale.policy_number || undefined,
                    start_sale_date: renewSaleDto.startDate,
                    end_sale_date: renewSaleDto.endDate,
                    net_amount: renewSaleDto.newSale.net_amount || originalSale.net_amount,
                    total_amount: renewSaleDto.newSale.total_amount || originalSale.total_amount,
                    condition_sale_id: renewSaleDto.newSale.condition_sale_id || originalSale.condition_sale_id || undefined,
                    currency_id: renewSaleDto.newSale.currency_id || originalSale.currency_id || undefined,
                    cia_company_id: renewSaleDto.newSale.cia_company_id || originalSale.cia_company_id || undefined,
                    insurance_line_cia_sale_id: renewSaleDto.newSale.insurance_line_cia_sale_id || originalSale.insurance_line_cia_sale_id || undefined,
                    customer_id: renewSaleDto.keepCustomerInfo ? originalSale.customer_id : (renewSaleDto.newSale.customer_id || originalSale.customer_id),
                    account_id: renewSaleDto.accountId
                }
            });
            return this.saleBuilder
                .withSaleId(newSale.sale_id)
                .withPolicyNumber(newSale.policy_number || undefined)
                .withStartSaleDate(newSale.start_sale_date || undefined)
                .withEndSaleDate(newSale.end_sale_date || undefined)
                .withNetAmount(Number(newSale.net_amount) || 0)
                .withTotalAmount(Number(newSale.total_amount) || 0)
                .withConditionSaleId(newSale.condition_sale_id || undefined)
                .withCurrencyId(newSale.currency_id || undefined)
                .withCiaCompanyId(newSale.cia_company_id || undefined)
                .withInsuranceLineCiaSaleId(newSale.insurance_line_cia_sale_id || undefined)
                .withCustomerId(newSale.customer_id)
                .withAccountId(newSale.account_id)
                .build();
        });
    }
};
exports.PrismaRenewSaleTransaction = PrismaRenewSaleTransaction;
exports.PrismaRenewSaleTransaction = PrismaRenewSaleTransaction = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.SalesServicePrismaService])
], PrismaRenewSaleTransaction);
//# sourceMappingURL=prisma-renew-sale.transaction.js.map