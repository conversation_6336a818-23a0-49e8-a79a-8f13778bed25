import { SalesServicePrismaService } from "../prisma.service";
import { IDeleteSaleTransaction } from "../../../domain/ports/transactions/delete-sale.transaction";
import { DeleteSaleDto } from "../../../domain/ports/transactions/dtos/delete-sale.dto";
export declare class PrismaDeleteSaleTransaction implements IDeleteSaleTransaction {
    private readonly _prismaService;
    constructor(_prismaService: SalesServicePrismaService);
    run(deleteSaleDto: DeleteSaleDto): Promise<void>;
}
