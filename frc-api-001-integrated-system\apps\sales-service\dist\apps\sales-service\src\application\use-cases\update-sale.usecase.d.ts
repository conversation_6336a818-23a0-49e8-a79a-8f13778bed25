import { IUpdateSaleTransaction } from "../../domain/ports/transactions/update-sale.transaction";
import { Sale } from "../../domain/models/sale";
import { IJwtService } from "../../domain/ports/services/jwt.service";
export declare const UPDATE_SALE_USE_CASE: unique symbol;
export declare class UpdateSaleUseCase {
    private readonly _updateSaleTransaction;
    private readonly _jwtService;
    private readonly saleBuilder;
    private readonly customerBuilder;
    private readonly locationBuilder;
    constructor(_updateSaleTransaction: IUpdateSaleTransaction, _jwtService: IJwtService);
    execute(saleId: number, saleData: any, token: string): Promise<Sale>;
}
