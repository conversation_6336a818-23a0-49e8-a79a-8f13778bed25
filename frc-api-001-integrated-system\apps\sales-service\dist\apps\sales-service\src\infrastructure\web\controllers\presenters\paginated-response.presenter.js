"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedResponsePresenter = void 0;
const api_response_presenter_1 = require("./api-response.presenter");
class PaginatedResponsePresenter extends api_response_presenter_1.ApiResponsePresenter {
    static fromPagination(items, total, page, limit, message = 'Datos recuperados exitosamente') {
        const totalPages = Math.ceil(total / limit);
        const paginatedData = {
            items,
            meta: {
                total,
                page,
                limit,
                totalPages,
                hasNextPage: page < totalPages,
                hasPreviousPage: page > 1
            }
        };
        return new PaginatedResponsePresenter(true, message, paginatedData);
    }
}
exports.PaginatedResponsePresenter = PaginatedResponsePresenter;
//# sourceMappingURL=paginated-response.presenter.js.map