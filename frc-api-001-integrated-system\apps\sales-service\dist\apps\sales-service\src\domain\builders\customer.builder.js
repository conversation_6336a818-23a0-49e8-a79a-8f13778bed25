"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerBuilder = void 0;
const customer_1 = require("../models/customer");
class CustomerBuilder {
    customer;
    constructor() {
        this.reset();
    }
    reset() {
        this.customer = new customer_1.Customer();
    }
    build() {
        const builtCustomer = this.customer;
        this.reset();
        return builtCustomer;
    }
    withCustomerId(customerId) {
        this.customer.customer_id = customerId;
        return this;
    }
    withFullName(fullName) {
        this.customer.full_name = fullName;
        return this;
    }
    withDoi(doi) {
        this.customer.doi = doi;
        return this;
    }
    withPhoneNumber(phoneNumber) {
        this.customer.phone_number = phoneNumber;
        return this;
    }
    withMobileNumber(mobileNumber) {
        this.customer.mobile_number = mobileNumber;
        return this;
    }
    withDocumentTypeId(documentTypeId) {
        this.customer.document_type_id = documentTypeId;
        return this;
    }
    withLocationId(locationId) {
        this.customer.location_id = locationId;
        return this;
    }
}
exports.CustomerBuilder = CustomerBuilder;
//# sourceMappingURL=customer.builder.js.map