import { Injectable } from "@nestjs/common";
import { SalesServicePrismaService } from "../prisma.service";
import { CATALOG_REPOSITORY, ICatalogRepository } from "../../../domain/ports/repositories/catalog.repository";
import { CiaCompany } from "../../../domain/models/cia-company";
import { ConditionSale } from "../../../domain/models/condition-sale";
import { Currency } from "../../../domain/models/currency";
import { DocumentType } from "../../../domain/models/document-type";
import { InsuranceLineCiaSale } from "../../../domain/models/insurance-line-cia-sale";

@Injectable()
export class PrismaCatalogRepository implements ICatalogRepository {
  constructor(
    private readonly _prismaService: SalesServicePrismaService,
  ) {}

  // Compañías de seguros
  async getAllCiaCompanies(): Promise<CiaCompany[]> {
    const companies = await this._prismaService.ciaCompany.findMany();
    
    return companies.map(company => {
      const ciaCompany = new CiaCompany();
      ciaCompany.cia_company_id = company.cia_company_id;
      ciaCompany.company_name = company.company_name;
      return ciaCompany;
    });
  }

  async getCiaCompanyById(id: number): Promise<CiaCompany | null> {
    const company = await this._prismaService.ciaCompany.findUnique({
      where: { cia_company_id: id }
    });
    
    if (!company) return null;
    
    const ciaCompany = new CiaCompany();
    ciaCompany.cia_company_id = company.cia_company_id;
    ciaCompany.company_name = company.company_name;
    return ciaCompany;
  }
  
  // Condiciones de venta
  async getAllConditionSales(): Promise<ConditionSale[]> {
    const conditions = await this._prismaService.conditionSale.findMany();
    
    return conditions.map(condition => {
      const conditionSale = new ConditionSale();
      conditionSale.condition_sale_id = condition.condition_sale_id;
      conditionSale.condition_name = condition.condition_name;
      return conditionSale;
    });
  }

  async getConditionSaleById(id: number): Promise<ConditionSale | null> {
    const condition = await this._prismaService.conditionSale.findUnique({
      where: { condition_sale_id: id }
    });
    
    if (!condition) return null;
    
    const conditionSale = new ConditionSale();
    conditionSale.condition_sale_id = condition.condition_sale_id;
    conditionSale.condition_name = condition.condition_name;
    return conditionSale;
  }
  
  // Monedas
  async getAllCurrencies(): Promise<Currency[]> {
    const currencies = await this._prismaService.currency.findMany();
    
    return currencies.map(currencyData => {
      const currency = new Currency();
      currency.currency_id = currencyData.currency_id;
      currency.currency_type = currencyData.currency_type;
      return currency;
    });
  }

  async getCurrencyById(id: number): Promise<Currency | null> {
    const currencyData = await this._prismaService.currency.findUnique({
      where: { currency_id: id }
    });
    
    if (!currencyData) return null;
    
    const currency = new Currency();
    currency.currency_id = currencyData.currency_id;
    currency.currency_type = currencyData.currency_type;
    return currency;
  }
  
  // Tipos de documento
  async getAllDocumentTypes(): Promise<DocumentType[]> {
    const documentTypes = await this._prismaService.documentType.findMany();
    
    return documentTypes.map(docType => {
      const documentType = new DocumentType();
      documentType.document_type_id = docType.document_type_id;
      documentType.document_name = docType.document_name;
      return documentType;
    });
  }

  async getDocumentTypeById(id: number): Promise<DocumentType | null> {
    const docType = await this._prismaService.documentType.findUnique({
      where: { document_type_id: id }
    });
    
    if (!docType) return null;
    
    const documentType = new DocumentType();
    documentType.document_type_id = docType.document_type_id;
    documentType.document_name = docType.document_name;
    return documentType;
  }
  
  // Ramos de seguro
  async getAllInsuranceLineCiaSales(): Promise<InsuranceLineCiaSale[]> {
    const insuranceLines = await this._prismaService.insuranceLineCiaSale.findMany();
    
    return insuranceLines.map(line => {
      const insuranceLine = new InsuranceLineCiaSale();
      insuranceLine.insurance_line_cia_sale_id = line.insurance_line_cia_sale_id;
      insuranceLine.insurance_name = line.insurance_name;
      return insuranceLine;
    });
  }

  async getInsuranceLineCiaSaleById(id: number): Promise<InsuranceLineCiaSale | null> {
    const line = await this._prismaService.insuranceLineCiaSale.findUnique({
      where: { insurance_line_cia_sale_id: id }
    });
    
    if (!line) return null;
    
    const insuranceLine = new InsuranceLineCiaSale();
    insuranceLine.insurance_line_cia_sale_id = line.insurance_line_cia_sale_id;
    insuranceLine.insurance_name = line.insurance_name;
    return insuranceLine;
  }
}
