import { Sale } from "../models/sale";

export class SaleBuilder {
  private sale: Sale;

  constructor() {
    this.reset();
  }

  reset(): void {
    this.sale = new Sale();
  }

  build(): Sale {
    const builtSale = this.sale;
    this.reset();
    return builtSale;
  }

  withSaleId(saleId: number): this {
    this.sale.sale_id = saleId;
    return this;
  }

  withPolicyNumber(policyNumber?: string): this {
    this.sale.policy_number = policyNumber;
    return this;
  }

  withStartSaleDate(startSaleDate?: Date): this {
    this.sale.start_sale_date = startSaleDate;
    return this;
  }

  withEndSaleDate(endSaleDate?: Date): this {
    this.sale.end_sale_date = endSaleDate;
    return this;
  }

  withNetAmount(netAmount?: number): this {
    this.sale.net_amount = netAmount;
    return this;
  }

  withTotalAmount(totalAmount?: number): this {
    this.sale.total_amount = totalAmount;
    return this;
  }

  withCustomerId(customerId: number): this {
    this.sale.customer_id = customerId;
    return this;
  }

  withAccountId(accountId: number): this {
    this.sale.account_id = accountId;
    return this;
  }

  withConditionSaleId(conditionSaleId?: number): this {
    this.sale.condition_sale_id = conditionSaleId;
    return this;
  }

  withCurrencyId(currencyId?: number): this {
    this.sale.currency_id = currencyId;
    return this;
  }

  withCiaCompanyId(ciaCompanyId?: number): this {
    this.sale.cia_company_id = ciaCompanyId;
    return this;
  }

  withInsuranceLineCiaSaleId(insuranceLineCiaSaleId?: number): this {
    this.sale.insurance_line_cia_sale_id = insuranceLineCiaSaleId;
    return this;
  }
}
