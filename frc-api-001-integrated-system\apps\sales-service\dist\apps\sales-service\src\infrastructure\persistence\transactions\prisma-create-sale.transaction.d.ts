import { SalesServicePrismaService } from "../prisma.service";
import { ICreateSaleTransaction } from "../../../domain/ports/transactions/create-sale.transaction";
import { CreateSaleDto } from "../../../domain/ports/transactions/dtos/create-sale.dto";
import { Sale } from "../../../domain/models/sale";
export declare class PrismaCreateSaleTransaction implements ICreateSaleTransaction {
    private readonly _prismaService;
    private readonly saleBuilder;
    constructor(_prismaService: SalesServicePrismaService);
    run(createSaleDto: CreateSaleDto): Promise<Sale>;
}
